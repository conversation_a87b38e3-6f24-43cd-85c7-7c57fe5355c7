# Proxy Round Robin Setup for Hyperliquid

<PERSON><PERSON> thống này triển khai Round Robin proxy cho <PERSON><PERSON><PERSON> request đến Hyperliquid API để tránh rate limiting và tăng tính ổn định.

## Cấu hình

### 1. Environment Variables

Thêm biến môi trường `RAW_PROXIES` vào file `.env`:

```bash
RAW_PROXIES=**********************************************,********************************************,*********************************************,*********************************************,*********************************************,**********************************************,**********************************************,*********************************************,**********************************************,**********************************************
```

### 2. Config.yaml

Cấu hình proxy đã được thêm vào `config.yaml`:

```yaml
proxy:
  enabled: true
  proxy-list: "{{ index . "RAW_PROXIES" }}"
  test-url: "https://api.hyperliquid.xyz/info"
  test-on-init: false
```

- `enabled`: Bật/tắt proxy
- `proxy-list`: Danh sách proxy (lấy từ environment variable)
- `test-url`: URL để test proxy
- `test-on-init`: Test proxy khi khởi động ứng dụng

## Cách hoạt động

### 1. Proxy Manager

- **Round Robin**: Tự động xoay vòng giữa các proxy
- **Fallback**: Nếu không có proxy, sử dụng direct connection
- **Thread-safe**: An toàn khi sử dụng đồng thời

### 2. HTTP Client Integration

Các function sau đã được cập nhật để sử dụng proxy:

- `pkg/hyperliquid/api.go`:
  - `CrawSymbolListFromAPI()`
  - `GetMainPageURL()`
  - `GetAccountSummaryFromAPI()`
  - `GetUserTradeHistoryFromAPI()`

- `internal/service/verify/get_user_fills.go`:
  - `GetUserFills()`

- `internal/utils/http.go`:
  - `SendWithProxy()` method

### 3. Automatic Proxy Selection

Mỗi request sẽ tự động chọn proxy tiếp theo trong danh sách theo thứ tự Round Robin.

## Testing

### 1. Test Proxy Manager

```bash
go run test_proxy.go
```

### 2. Test trong ứng dụng

Khi khởi động ứng dụng, proxy manager sẽ được khởi tạo và log thông tin:

```
Proxy manager initialized proxy_count=10 proxies=[...]
```

### 3. Enable Proxy Testing

Để test proxy khi khởi động, set `test-on-init: true` trong config:

```yaml
proxy:
  enabled: true
  proxy-list: "{{ index . "RAW_PROXIES" }}"
  test-url: "https://api.hyperliquid.xyz/info"
  test-on-init: true
```

## Monitoring

### Logs

- Proxy initialization: `Proxy manager initialized`
- Proxy test results: `Proxy test passed/failed`
- Request routing: Tự động sử dụng proxy cho mọi Hyperliquid request

### Health Check

Có thể sử dụng các method sau để kiểm tra proxy:

```go
// Get proxy manager
pm := initializer.GetProxyManager()

// Test all proxies
testResults := pm.TestAllProxies("https://api.hyperliquid.xyz/info", 10*time.Second)

// Get working proxies only
workingProxies := pm.GetWorkingProxies("https://api.hyperliquid.xyz/info", 10*time.Second)
```

## Troubleshooting

### 1. Proxy không hoạt động

- Kiểm tra format của `RAW_PROXIES`
- Kiểm tra proxy có còn hoạt động không
- Xem logs để biết lỗi cụ thể

### 2. Tắt proxy

Set `enabled: false` trong config:

```yaml
proxy:
  enabled: false
```

### 3. Thêm/xóa proxy

Cập nhật biến `RAW_PROXIES` và restart ứng dụng.

## Architecture

```
Request → createHTTPClient() → ProxyManager.CreateHTTPClient() → Round Robin Selection → HTTP Request with Proxy
```

Nếu proxy không available hoặc bị tắt, sẽ fallback về direct connection.
