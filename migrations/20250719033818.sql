-- Modify "rango_meta_tokens" table
ALTER TABLE "public"."rango_meta_tokens" ADD COLUMN "is_native" boolean NULL DEFAULT false;
-- Create "hyperliquid_verify_order" table
CREATE TABLE "public"."hyperliquid_verify_order" (
  "id" uuid NOT NULL,
  "created_at" timestamptz NULL,
  "updated_at" timestamptz NULL,
  "order_created_at" timestamptz NULL,
  "cloid" text NULL,
  "user_id" uuid NULL,
  "user_address" text NULL,
  "side" text NULL,
  "order_type" text NULL,
  "symbol" text NULL,
  "is_buy" boolean NULL,
  "leverage" bigint NULL,
  "margin" text NULL,
  "is_market" boolean NULL,
  "trigger_px" text NULL,
  "tpsl" text NULL,
  "tif" text NULL,
  "base" text NULL,
  "quote" text NULL,
  "size" numeric(20,8) NULL,
  "price" numeric(20,8) NULL,
  "avg_price" numeric(20,8) NULL,
  "build_fee" numeric(20,8) NULL,
  "total_fee" numeric(20,8) NULL,
  "fee_bp" bigint NULL,
  "build_address" text NULL,
  "status" text NULL,
  "oid" bigint NULL,
  "total_sz" text NULL,
  "source_data" text NULL,
  "is_verified" boolean NULL DEFAULT false,
  "csv_verified" boolean NULL DEFAULT false,
  "expected_fee" numeric(10,6) NULL,
  "actual_fee" numeric(10,6) NULL,
  "fee_valid" boolean NULL DEFAULT false,
  "affiliate_event_sent" boolean NULL DEFAULT false,
  PRIMARY KEY ("id")
);
-- Create index "idx_hyperliquid_verify_order_cloid" to table: "hyperliquid_verify_order"
CREATE UNIQUE INDEX "idx_hyperliquid_verify_order_cloid" ON "public"."hyperliquid_verify_order" ("cloid", "cloid");
-- Create index "idx_hyperliquid_verify_order_csv_verified" to table: "hyperliquid_verify_order"
CREATE INDEX "idx_hyperliquid_verify_order_csv_verified" ON "public"."hyperliquid_verify_order" ("csv_verified");
-- Create index "idx_hyperliquid_verify_order_is_verified" to table: "hyperliquid_verify_order"
CREATE INDEX "idx_hyperliquid_verify_order_is_verified" ON "public"."hyperliquid_verify_order" ("is_verified");
-- Create index "idx_hyperliquid_verify_order_status" to table: "hyperliquid_verify_order"
CREATE INDEX "idx_hyperliquid_verify_order_status" ON "public"."hyperliquid_verify_order" ("status");
-- Create index "idx_hyperliquid_verify_order_user_address" to table: "hyperliquid_verify_order"
CREATE INDEX "idx_hyperliquid_verify_order_user_address" ON "public"."hyperliquid_verify_order" ("user_address");
-- Create index "idx_hyperliquid_verify_order_user_id" to table: "hyperliquid_verify_order"
CREATE INDEX "idx_hyperliquid_verify_order_user_id" ON "public"."hyperliquid_verify_order" ("user_id");
-- Create "relay_meta_chains" table
CREATE TABLE "public"."relay_meta_chains" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "created_at" timestamptz NULL,
  "updated_at" timestamptz NULL,
  "deleted_at" timestamptz NULL,
  "chain_id_hex" text NULL,
  "chain_id" text NULL,
  "name" text NULL,
  "display_name" text NULL,
  "http_rpc_url" text NULL,
  "ws_rpc_url" text NULL,
  "explorer_url" text NULL,
  "explorer_name" text NULL,
  "deposit_enabled" boolean NULL,
  "token_support" text NULL,
  "disabled" boolean NULL,
  "partial_disable_limit" bigint NULL,
  "block_production_lagging" boolean NULL,
  "withdrawal_fee" bigint NULL,
  "deposit_fee" bigint NULL,
  "surge_enabled" boolean NULL,
  "icon_url" text NULL,
  "contracts" bytea NULL,
  "vm_type" text NULL,
  "base_chain_id" bigint NULL,
  "solver_addresses" text NULL,
  "tags" text NULL,
  "logo_url" text NULL,
  "brand_color" text NULL,
  "explorer_paths" text NULL,
  "is_active" boolean NULL,
  PRIMARY KEY ("id")
);
-- Create index "idx_relay_meta_chains_chain_id" to table: "relay_meta_chains"
CREATE INDEX "idx_relay_meta_chains_chain_id" ON "public"."relay_meta_chains" ("chain_id");
-- Create index "idx_relay_meta_chains_chain_id_hex" to table: "relay_meta_chains"
CREATE UNIQUE INDEX "idx_relay_meta_chains_chain_id_hex" ON "public"."relay_meta_chains" ("chain_id_hex");
-- Create index "idx_relay_meta_chains_deleted_at" to table: "relay_meta_chains"
CREATE INDEX "idx_relay_meta_chains_deleted_at" ON "public"."relay_meta_chains" ("deleted_at");
-- Create index "idx_relay_meta_chains_display_name" to table: "relay_meta_chains"
CREATE INDEX "idx_relay_meta_chains_display_name" ON "public"."relay_meta_chains" ("display_name");
-- Create index "idx_relay_meta_chains_name" to table: "relay_meta_chains"
CREATE INDEX "idx_relay_meta_chains_name" ON "public"."relay_meta_chains" ("name");
-- Create "relay_meta_tokens" table
CREATE TABLE "public"."relay_meta_tokens" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "created_at" timestamptz NULL,
  "updated_at" timestamptz NULL,
  "deleted_at" timestamptz NULL,
  "chain_id_hex" text NULL,
  "token_id" text NULL,
  "address" text NULL,
  "chain_id" text NULL,
  "symbol" text NULL,
  "is_native" boolean NULL,
  "name" text NULL,
  "decimals" bigint NULL,
  "supports_bridging" boolean NULL,
  "withdrawal_fee" bigint NULL,
  "deposit_fee" bigint NULL,
  "surge_enabled" boolean NULL,
  "supports_permit" boolean NULL,
  "usd_price" text NULL,
  "logo_uri" text NULL,
  "is_active" boolean NULL,
  PRIMARY KEY ("id")
);
-- Create index "idx_chain_addr_token" to table: "relay_meta_tokens"
CREATE UNIQUE INDEX "idx_chain_addr_token" ON "public"."relay_meta_tokens" ("chain_id_hex", "token_id", "address");
-- Create index "idx_relay_meta_tokens_chain_id" to table: "relay_meta_tokens"
CREATE INDEX "idx_relay_meta_tokens_chain_id" ON "public"."relay_meta_tokens" ("chain_id");
-- Create index "idx_relay_meta_tokens_deleted_at" to table: "relay_meta_tokens"
CREATE INDEX "idx_relay_meta_tokens_deleted_at" ON "public"."relay_meta_tokens" ("deleted_at");
-- Create index "idx_relay_meta_tokens_symbol" to table: "relay_meta_tokens"
CREATE INDEX "idx_relay_meta_tokens_symbol" ON "public"."relay_meta_tokens" ("symbol");
