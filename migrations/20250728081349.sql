-- Create "relay_capacities" table
CREATE TABLE "public"."relay_capacities" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "created_at" timestamptz NULL,
  "updated_at" timestamptz NULL,
  "deleted_at" timestamptz NULL,
  "solver_address" text NULL,
  "chain_id" text NULL,
  "token_id" text NULL,
  "capacity_volume" text NULL,
  PRIMARY KEY ("id")
);
-- <PERSON><PERSON> index "idx_chain_token_solver" to table: "relay_capacities"
CREATE UNIQUE INDEX "idx_chain_token_solver" ON "public"."relay_capacities" ("solver_address", "chain_id", "token_id");
-- <PERSON>reate index "idx_relay_capacities_capacity_volume" to table: "relay_capacities"
CREATE INDEX "idx_relay_capacities_capacity_volume" ON "public"."relay_capacities" ("capacity_volume");
-- <PERSON>reate index "idx_relay_capacities_deleted_at" to table: "relay_capacities"
CREATE INDEX "idx_relay_capacities_deleted_at" ON "public"."relay_capacities" ("deleted_at");
