package main

import (
	"fmt"
	"io"
	"os"

	"ariga.io/atlas-provider-gorm/gormschema"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
)

func main() {
	// define models to auto generate
	stmts, err := gormschema.New("postgres").Load(
		&model.Affiliate{},
		&model.RangoMetaChains{},
		&model.RangoMetaSwappers{},
		&model.RangoMetaTokens{},
		&model.RangoSwaps{},
		&model.RangoHistory{},
		&model.Coin{},
		&model.CoinStatistic{},
		&model.UserCoin{},
		&model.CategoryCoin{},
		&model.Order{},
		&model.Position{},
		&model.UserNotificationSetting{},
		&model.UserWallet{},
		&model.UserBalance{},
		&model.RelayMetaChains{},
		&model.RelayMetaTokens{},
		&model.HyperLiquidVerifyOrder{},
		&model.RelayCapacity{},
	)
	if err != nil {
		fmt.Fprintf(os.Stderr, "failed to load gorm schema: %v\n", err)
		os.Exit(1)
	}
	io.WriteString(os.Stdout, stmts)
}
