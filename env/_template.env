STAGE=

REDIS_HOST={{ .REDIS_HOST }}
REDIS_PORT={{ .REDIS_PORT }}
REDIS_PASS={{ .REDIS_PASS }}

POSTGRES_HOST={{ .POSTGRES_HOST }}
POSTGRES_PORT={{ .POSTGRES_PORT }}
POSTGRES_USER={{ .POSTGRES_USER }}
POSTGRES_PASS={{ .POSTGRES_PASS }}

CLICKHOUSE_HOST={{ .CLICKHOUSE_HOST }}
CLICKHOUSE_PORT={{ .CLICKHOUSE_PORT }}
CLICKHOUSE_USER={{ .CLICKHOUSE_USER }}
CLICKHOUSE_PASS={{ .CLICKHOUSE_PASS }}

NATS_URL={{ .NATS_URL }}
NATS_USER={{ .NATS_USER }}
NATS_PASS={{ .NATS_PASS }}
NATS_USE_TLS={{ df .NATS_USE_TLS "false" }}

MEME_NATS_URL={{ .MEME_NATS_URL }}
MEME_NATS_USER={{ .MEME_NATS_USER }}
MEME_NATS_PASS={{ .MEME_NATS_PASS }}

COIN_MARKET_CAP_API_KEY={{ .MEME_NATS_PASS }}