package rango

import (
	"context"
	"crypto/ecdsa"
	"fmt"
	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	"log"
	"math/big"
)

const (
	PRIVATE_KEY  = ""
	PRIVATE_KEY2 = ""
)

const (
	BscRPC_URL           = "https://late-withered-model.bsc.quiknode.pro//"                                                              // Bsc RPC
	ArbRPC_URL           = "https://summer-distinguished-breeze.arbitrum-mainnet.quiknode.pro/6681c4b31fa02ee860081babc885d918c856a012/" // Arb RPC
	PolgonRPC_URL        = "https://virulent-frequent-meadow.matic.quiknode.pro//"                                                       // Arb RPC
	BscSWAP_CONTRACT     = "******************************************"                                                                  // Swap contract address
	PolygonSWAP_CONTRACT = ""                                                                                                            // Swap contract address
)

func SendTransaction(data []byte) (string, error) {
	client, err := ethclient.Dial(ArbRPC_URL)
	if err != nil {
		log.Fatalf("Failed to connect to RPC: %v", err)
	}
	defer client.Close()

	privateKey, err := crypto.HexToECDSA(PRIVATE_KEY)
	if err != nil {
		return "", fmt.Errorf("invalid private key: %v", err)
	}
	publicKey := privateKey.Public().(*ecdsa.PublicKey)
	fromAddress := crypto.PubkeyToAddress(*publicKey)
	fmt.Println("account :", fromAddress.String())
	balance, err := client.BalanceAt(context.Background(), fromAddress, nil)
	if err != nil {
		return "", fmt.Errorf("failed to get balance: %v", err)
	}
	fmt.Println("account balance:", balance.String())

	nonce, err := client.PendingNonceAt(context.Background(), fromAddress)
	if err != nil {
		return "", fmt.Errorf("failed to get nonce: %v", err)
	}

	gasPrice, err := client.SuggestGasPrice(context.Background())
	if err != nil {
		return "", fmt.Errorf("failed to get gas price: %v", err)
	}
	// Manually increase the gas price.
	gasPrice = new(big.Int).Mul(gasPrice, big.NewInt(2))
	fmt.Println("current  gas price:", gasPrice.String())

	// estimate the  gasLimit
	msg := ethereum.CallMsg{
		From:     fromAddress,
		To:       &common.Address{},
		GasPrice: gasPrice,
		Value:    big.NewInt(0),
		Data:     data,
	}
	gasLimit, err := client.EstimateGas(context.Background(), msg)
	if err != nil {
		fmt.Println("Gas estimate fail,use default value")
	} else {
		gasLimit = uint64(float64(gasLimit) * 1.2) // increase 20%
	}
	gasLimit = 500000 * 2
	fmt.Println("estimate gasLimit:", gasLimit)

	// Calculate the transaction fee.
	totalGasFee := new(big.Int).Mul(big.NewInt(int64(gasLimit)), gasPrice)
	if balance.Cmp(totalGasFee) < 0 {
		return "", fmt.Errorf("balance not enough: need %s ETH, but has %s ETH",
			new(big.Float).Quo(new(big.Float).SetInt(totalGasFee), big.NewFloat(1e18)),
			new(big.Float).Quo(new(big.Float).SetInt(balance), big.NewFloat(1e18)))
	}
	amount := big.NewInt(0)                   // The default value is 0. If the contract requires ETH, set the correct value.
	amount.SetString("10000000000000000", 10) // For example, 0.01 ETH. Adjust it according to the contract requirements.
	// Create a transaction
	tx := types.NewTransaction(nonce, common.HexToAddress(BscSWAP_CONTRACT), amount, gasLimit, gasPrice, data)

	chainID, err := client.NetworkID(context.Background())
	if err != nil {
		return "", fmt.Errorf("failed to get chain ID: %v", err)
	}

	signedTx, err := types.SignTx(tx, types.NewEIP155Signer(chainID), privateKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign transaction: %v", err)
	}

	err = client.SendTransaction(context.Background(), signedTx)
	if err != nil {
		return "", fmt.Errorf("failed to send transaction: %v", err)
	}

	fmt.Println("SendTransaction over，TxHash:", signedTx.Hash().Hex())
	return signedTx.Hash().Hex(), nil
}
func PolygonSendTransaction(data []byte) (string, error) {
	client, err := ethclient.Dial(ArbRPC_URL)
	if err != nil {
		log.Fatalf("Failed to connect to RPC: %v", err)
	}
	defer client.Close()

	privateKey, err := crypto.HexToECDSA(PRIVATE_KEY)
	if err != nil {
		return "", fmt.Errorf("invalid private key: %v", err)
	}
	publicKey := privateKey.Public().(*ecdsa.PublicKey)
	fromAddress := crypto.PubkeyToAddress(*publicKey)
	fmt.Println("account :", fromAddress.String())
	balance, err := client.BalanceAt(context.Background(), fromAddress, nil)
	if err != nil {
		return "", fmt.Errorf("failed to get balance: %v", err)
	}
	fmt.Println("account balance:", balance.String())

	nonce, err := client.PendingNonceAt(context.Background(), fromAddress)
	if err != nil {
		return "", fmt.Errorf("failed to get nonce: %v", err)
	}

	gasPrice, err := client.SuggestGasPrice(context.Background())
	if err != nil {
		return "", fmt.Errorf("failed to get gas price: %v", err)
	}
	// Manually increase the gas price.
	gasPrice = new(big.Int).Mul(gasPrice, big.NewInt(2))
	fmt.Println("current  gas price:", gasPrice.String())
	toAddress := common.HexToAddress(PolygonSWAP_CONTRACT)
	// estimate the  gasLimit
	msg := ethereum.CallMsg{
		From:     fromAddress,
		To:       &toAddress,
		GasPrice: gasPrice,
		Value:    big.NewInt(0),
		Data:     data,
	}
	gasLimit, err := client.EstimateGas(context.Background(), msg)
	if err != nil {
		fmt.Println("Gas estimate fail, use default value")
		gasLimit = 800000 // fallback value
	} else {
		gasLimit = uint64(float64(gasLimit) * 1.2)
	}

	gasLimit = ********
	fmt.Println("estimate gasLimit:", gasLimit)

	// Calculate the transaction fee.
	totalGasFee := new(big.Int).Mul(big.NewInt(int64(gasLimit)), gasPrice)
	if balance.Cmp(totalGasFee) < 0 {
		return "", fmt.Errorf("balance not enough: need %s ETH, but has %s ETH",
			new(big.Float).Quo(new(big.Float).SetInt(totalGasFee), big.NewFloat(1e18)),
			new(big.Float).Quo(new(big.Float).SetInt(balance), big.NewFloat(1e18)))
	}
	amount := big.NewInt(0)         // The default value is 0. If the contract requires ETH, set the correct value.
	amount.SetString("1100000", 10) // For example, 0.01 ETH. Adjust it according to the contract requirements.
	// Create a transaction
	tx := types.NewTransaction(nonce, common.HexToAddress(PolygonSWAP_CONTRACT), amount, gasLimit, gasPrice, data)
	chainID, err := client.NetworkID(context.Background())
	if err != nil {
		return "", fmt.Errorf("failed to get chain ID: %v", err)
	}

	signedTx, err := types.SignTx(tx, types.NewEIP155Signer(chainID), privateKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign transaction: %v", err)
	}

	err = client.SendTransaction(context.Background(), signedTx)
	if err != nil {
		return "", fmt.Errorf("failed to send transaction: %v", err)
	}

	fmt.Println("SendTransaction over，TxHash:", signedTx.Hash().Hex())
	return signedTx.Hash().Hex(), nil
}
