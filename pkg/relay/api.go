package relay

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
)

const RelayUrl = "https://api.relay.link"

func GetMetaData() ([]*Chain, error) {
	url := RelayUrl + "/chains"
	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	var metaData MetaData
	if err := json.Unmarshal(body, &metaData); err != nil {
		return nil, err
	}
	return metaData.Chains, nil
}

func GetRelayQuote(req *QuoteRequest) (*QuoteResponse, error) {
	url := "https://api.relay.link/quote"
	body, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(body))
	if err != nil {
		return nil, err
	}
	httpReq.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	var quoteResp QuoteResponse
	err = json.Unmarshal(respBody, &quoteResp)
	if err != nil {
		return nil, err
	}
	if quoteResp.ErrorCode != "" || quoteResp.Message != "" {
		return nil, fmt.Errorf("error from relay: %s %s", quoteResp.ErrorCode, quoteResp.Message)
	}
	return &quoteResp, nil
}

func GetStatus(requestId string) (*IntentStatusResponse, error) {
	url := RelayUrl + "/intents/status?requestId=" + requestId + "&referrer=relay.link"
	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	var statusResp IntentStatusResponse
	if err := json.Unmarshal(body, &statusResp); err != nil {
		return nil, err
	}
	return &statusResp, nil
}

func GetConfig(originChainId, destinationChainId int64, user, currency string) (*GetConfigResponse, error) {
	if originChainId == 792703809 && strings.HasPrefix(user, "0x") {
		// If originChainId is 0 and user is an address, we assume it's the same chain
		return nil, fmt.Errorf("invalid user in chain id= %d user=%s", originChainId, user)
	}
	endpoint := fmt.Sprintf("%s/config/v2", RelayUrl)
	params := url.Values{}

	params.Set("originChainId", fmt.Sprintf("%d", originChainId))

	if destinationChainId != 0 {
		params.Set("destinationChainId", fmt.Sprintf("%d", destinationChainId))
	}
	if currency != "" {
		params.Set("currency", currency)
	}
	if user != "" {
		params.Set("user", user)
	}

	reqUrl := fmt.Sprintf("%s?%s", endpoint, params.Encode())
	resp, err := http.Get(reqUrl)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status: %s", resp.Status)
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var configResp GetConfigResponse
	if err := json.Unmarshal(body, &configResp); err != nil {
		return nil, err
	}

	return &configResp, nil
}
