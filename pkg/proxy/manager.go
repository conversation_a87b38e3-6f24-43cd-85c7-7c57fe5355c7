package proxy

import (
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"
)

// ProxyManager manages a list of proxies with round-robin rotation
type ProxyManager struct {
	proxies []string
	current int
	mutex   sync.RWMutex
}

// NewProxyManager creates a new proxy manager with the given proxy list
func NewProxyManager(proxyList string) *ProxyManager {
	if proxyList == "" {
		return &ProxyManager{
			proxies: []string{},
			current: 0,
		}
	}

	proxies := strings.Split(proxyList, ",")
	// Trim whitespace from each proxy
	for i, proxy := range proxies {
		proxies[i] = strings.TrimSpace(proxy)
	}

	return &ProxyManager{
		proxies: proxies,
		current: 0,
	}
}

// GetNextProxy returns the next proxy in round-robin fashion
func (pm *ProxyManager) GetNextProxy() string {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	if len(pm.proxies) == 0 {
		return ""
	}

	proxy := pm.proxies[pm.current]
	pm.current = (pm.current + 1) % len(pm.proxies)
	return proxy
}

// GetProxyCount returns the number of available proxies
func (pm *ProxyManager) GetProxyCount() int {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()
	return len(pm.proxies)
}

// CreateHTTPClient creates an HTTP client with the next available proxy
func (pm *ProxyManager) CreateHTTPClient(timeout time.Duration) *http.Client {
	proxyURL := pm.GetNextProxy()
	
	if proxyURL == "" {
		// No proxy available, return default client
		return &http.Client{
			Timeout: timeout,
		}
	}

	// Parse proxy URL
	parsedProxy, err := url.Parse(proxyURL)
	if err != nil {
		// If proxy URL is invalid, return default client
		return &http.Client{
			Timeout: timeout,
		}
	}

	// Create transport with proxy
	transport := &http.Transport{
		Proxy: http.ProxyURL(parsedProxy),
	}

	return &http.Client{
		Transport: transport,
		Timeout:   timeout,
	}
}

// CreateHTTPClientWithSpecificProxy creates an HTTP client with a specific proxy
func (pm *ProxyManager) CreateHTTPClientWithSpecificProxy(proxyURL string, timeout time.Duration) *http.Client {
	if proxyURL == "" {
		return &http.Client{
			Timeout: timeout,
		}
	}

	parsedProxy, err := url.Parse(proxyURL)
	if err != nil {
		return &http.Client{
			Timeout: timeout,
		}
	}

	transport := &http.Transport{
		Proxy: http.ProxyURL(parsedProxy),
	}

	return &http.Client{
		Transport: transport,
		Timeout:   timeout,
	}
}

// GetAllProxies returns all available proxies
func (pm *ProxyManager) GetAllProxies() []string {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()
	
	result := make([]string, len(pm.proxies))
	copy(result, pm.proxies)
	return result
}

// AddProxy adds a new proxy to the list
func (pm *ProxyManager) AddProxy(proxyURL string) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	
	proxyURL = strings.TrimSpace(proxyURL)
	if proxyURL != "" {
		pm.proxies = append(pm.proxies, proxyURL)
	}
}

// RemoveProxy removes a proxy from the list
func (pm *ProxyManager) RemoveProxy(proxyURL string) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	
	for i, proxy := range pm.proxies {
		if proxy == proxyURL {
			pm.proxies = append(pm.proxies[:i], pm.proxies[i+1:]...)
			// Adjust current index if necessary
			if pm.current >= len(pm.proxies) && len(pm.proxies) > 0 {
				pm.current = 0
			}
			break
		}
	}
}

// TestProxy tests if a proxy is working by making a simple HTTP request
func (pm *ProxyManager) TestProxy(proxyURL string, testURL string, timeout time.Duration) error {
	client := pm.CreateHTTPClientWithSpecificProxy(proxyURL, timeout)
	
	resp, err := client.Get(testURL)
	if err != nil {
		return fmt.Errorf("proxy test failed: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode >= 400 {
		return fmt.Errorf("proxy test failed with status code: %d", resp.StatusCode)
	}
	
	return nil
}

// TestAllProxies tests all proxies and returns a map of proxy -> error (nil if working)
func (pm *ProxyManager) TestAllProxies(testURL string, timeout time.Duration) map[string]error {
	results := make(map[string]error)
	
	proxies := pm.GetAllProxies()
	for _, proxy := range proxies {
		results[proxy] = pm.TestProxy(proxy, testURL, timeout)
	}
	
	return results
}

// GetWorkingProxies returns only the proxies that pass the test
func (pm *ProxyManager) GetWorkingProxies(testURL string, timeout time.Duration) []string {
	var workingProxies []string
	
	testResults := pm.TestAllProxies(testURL, timeout)
	for proxy, err := range testResults {
		if err == nil {
			workingProxies = append(workingProxies, proxy)
		}
	}
	
	return workingProxies
}

// UpdateProxies replaces the current proxy list with a new one
func (pm *ProxyManager) UpdateProxies(proxyList string) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	
	if proxyList == "" {
		pm.proxies = []string{}
	} else {
		proxies := strings.Split(proxyList, ",")
		// Trim whitespace from each proxy
		for i, proxy := range proxies {
			proxies[i] = strings.TrimSpace(proxy)
		}
		pm.proxies = proxies
	}
	
	pm.current = 0
}
