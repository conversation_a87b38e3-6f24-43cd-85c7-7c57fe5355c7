package proxy

import (
	"bytes"
	"io"
	"net/http"
	"net/url"
	"time"
)

// ProxyHTTPClient wraps http.Client with proxy support
type ProxyHTTPClient struct {
	proxyManager *ProxyManager
	timeout      time.Duration
}

// NewProxyHTTPClient creates a new HTTP client with proxy support
func NewProxyHTTPClient(proxyManager *ProxyManager, timeout time.Duration) *ProxyHTTPClient {
	return &ProxyHTTPClient{
		proxyManager: proxyManager,
		timeout:      timeout,
	}
}

// Do performs an HTTP request using the next available proxy
func (c *ProxyHTTPClient) Do(req *http.Request) (*http.Response, error) {
	client := c.proxyManager.CreateHTTPClient(c.timeout)
	return client.Do(req)
}

// Get performs a GET request using the next available proxy
func (c *ProxyHTTPClient) Get(url string) (*http.Response, error) {
	client := c.proxyManager.CreateHTTPClient(c.timeout)
	return client.Get(url)
}

// Post performs a POST request using the next available proxy
func (c *ProxyHTTPClient) Post(url, contentType string, body io.Reader) (*http.Response, error) {
	client := c.proxyManager.CreateHTTPClient(c.timeout)
	return client.Post(url, contentType, body)
}

// PostJSON performs a POST request with JSON body using the next available proxy
func (c *ProxyHTTPClient) PostJSON(url string, jsonData []byte) (*http.Response, error) {
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, err
	}
	
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("User-Agent", "go-http-client/1.1")
	
	return c.Do(req)
}

// PostForm performs a POST request with form data using the next available proxy
func (c *ProxyHTTPClient) PostForm(url string, data url.Values) (*http.Response, error) {
	client := c.proxyManager.CreateHTTPClient(c.timeout)
	return client.PostForm(url, data)
}

// Head performs a HEAD request using the next available proxy
func (c *ProxyHTTPClient) Head(url string) (*http.Response, error) {
	client := c.proxyManager.CreateHTTPClient(c.timeout)
	return client.Head(url)
}

// SetTimeout updates the timeout for HTTP requests
func (c *ProxyHTTPClient) SetTimeout(timeout time.Duration) {
	c.timeout = timeout
}

// GetTimeout returns the current timeout
func (c *ProxyHTTPClient) GetTimeout() time.Duration {
	return c.timeout
}

// GetProxyManager returns the underlying proxy manager
func (c *ProxyHTTPClient) GetProxyManager() *ProxyManager {
	return c.proxyManager
}
