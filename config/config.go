package config

type Server struct {
	Zap            Zap             `mapstructure:"zap" json:"zap" yaml:"zap"`
	Redis          Redis           `mapstructure:"redis" json:"redis" yaml:"redis"`
	RedisList      []Redis         `mapstructure:"redis-list" json:"redis-list" yaml:"redis-list"`
	Mongo          Mongo           `mapstructure:"mongo" json:"mongo" yaml:"mongo"`
	System         System          `mapstructure:"system" json:"system" yaml:"system"`
	Pgsql          Pgsql           `mapstructure:"pgsql" json:"pgsql" yaml:"pgsql"`
	Clickhouse     Clickhouse      `mapstructure:"clickhouse" json:"clickhouse" yaml:"clickhouse"`
	Grpc           Grpc            `mapstructure:"grpc" json:"grpc" yaml:"grpc"`
	Temporal       Temporal        `mapstructure:"temporal" json:"temporal" yaml:"temporal"`
	Mqtt           Mqtt            `mapstructure:"mqtt" json:"mqtt" yaml:"mqtt"`
	DBList         []SpecializedDB `mapstructure:"db-list" json:"db-list" yaml:"db-list"`
	Cors           CORS            `mapstructure:"cors" json:"cors" yaml:"cors"`
	ServerName     string          `mapstructure:"server-name" json:"server-name" yaml:"server-name"`
	Hyperliquid    Hyperliquid     `mapstructure:"hyperliquid" json:"hyperliquid" yaml:"hyperliquid"`
	CoinmMarketCap CoinMarketCap   `mapstructure:"coinmarketcap" json:"coinmarketcap" yaml:"coinmarketcap"`
	CronTasks      []Task          `mapstructure:"cron-tasks" json:"cron-tasks" yaml:"cron-tasks"`
	Proxy          Proxy           `mapstructure:"proxy" json:"proxy" yaml:"proxy"`
	NatsMeme       Nats            `mapstructure:"nats-meme" json:"nats-meme" yaml:"nats-meme"`
	NatsDex        Nats            `mapstructure:"nats-dex" json:"nats-dex" yaml:"nats-dex"`
}
