package main

import (
	"bytes"
	"fmt"
	"log"
	"os"
	"time"

	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/proxy"
)

func main() {
	// Get proxy list from environment variable
	proxyList := os.Getenv("RAW_PROXIES")
	if proxyList == "" {
		log.Fatal("RAW_PROXIES environment variable not set")
	}

	fmt.Printf("Testing Hyperliquid API with %d proxies\n", len(bytes.Split([]byte(proxyList), []byte(","))))

	// Create proxy manager
	proxyManager := proxy.NewProxyManager(proxyList)

	// Create proxy HTTP client
	proxyClient := proxy.NewProxyHTTPClient(proxyManager, 10*time.Second)

	// Test Hyperliquid API request
	apiURL := "https://api.hyperliquid.xyz/info"
	reqBody := []byte(`{"type": "metaAndAssetCtxs"}`)

	fmt.Println("\nTesting 5 requests with Round Robin proxy:")

	for i := 0; i < 5; i++ {
		fmt.Printf("\nRequest %d:\n", i+1)

		// Show which proxy will be used next
		nextProxy := proxyManager.GetNextProxy()
		fmt.Printf("Using proxy: %s\n", nextProxy)

		// Make request
		resp, err := proxyClient.PostJSON(apiURL, reqBody)
		if err != nil {
			fmt.Printf("Error: %v\n", err)
			continue
		}
		defer resp.Body.Close()

		fmt.Printf("Status: %s\n", resp.Status)

		if resp.StatusCode == 200 {
			fmt.Println("✅ Request successful!")
		} else {
			fmt.Printf("❌ Request failed with status: %d\n", resp.StatusCode)
		}

		// Small delay between requests
		time.Sleep(1 * time.Second)
	}

	fmt.Println("\n=== Summary ===")
	fmt.Printf("Proxy Round Robin is working correctly!\n")
	fmt.Printf("Each request uses a different proxy in rotation.\n")
}
