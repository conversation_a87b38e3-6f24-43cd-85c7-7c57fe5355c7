package config

import (
	"errors"
	"fmt"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/constant"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
)

type MetaData struct {
	Chains      []*ChainMeta          `json:"chains"`
	ChainFilter map[string]*ChainMeta `json:"chainFilter"` //chainHexId
}

type ChainMeta struct {
	ChainId     string                `json:"chainId"`
	ChainImage  string                `json:"chainImage"`
	ChainName   string                `json:"chainName"`
	Tokens      []*TokenMeta          `json:"tokens"`
	TokenFilter map[string]*TokenMeta `json:"tokenFilter"` //chainHexId::tokenSymbol
}

func (c *ChainMeta) Key() string {
	return c.ChainId
}

func (c *ChainMeta) Assign(chainId, chainImage, chainName string) {
	chainId = utils.HexToDecimalString(chainId)
	*c = ChainMeta{
		ChainId:    chainId,
		ChainImage: chainImage,
		ChainName:  chainName,
	}
}

func (c *ChainMeta) AssignTokens(tokens []*TokenMeta) {
	c.Tokens = tokens
	c.TokenFilter = make(map[string]*TokenMeta)
	for _, token := range tokens {
		c.TokenFilter[token.key()] = token
	}
}

func (c *ChainMeta) CombineMetaData(chainFilter map[string]*ChainMeta) error {
	chain, ok := chainFilter[c.ChainId]
	if ok {
		c.ChainImage = chain.ChainImage
	} else {
		return errors.New(fmt.Sprintf("chain %s not exists", c.ChainId))
	}
	for _, t := range c.Tokens {
		token, ok := chain.TokenFilter[t.key()]
		if !ok {
			continue
		}
		if t.Image == "" {
			t.Image = token.Image
		}
		if t.UsdPrice == 0 {
			t.UsdPrice = token.UsdPrice
		}
		for s, s2 := range token.Extra {
			t.Extra[s] = s2
		}
	}
	return nil
}

type TokenMeta struct {
	Id       string            `json:"id"`
	Address  string            `json:"address"`
	Symbol   string            `json:"symbol"`
	Name     string            `json:"name"`
	Image    string            `json:"image"`
	Decimals int               `json:"decimals"`
	UsdPrice float64           `json:"usdPrice"`
	Extra    map[string]string `json:"extra"` //rango:id, relay:id
}

func (t *TokenMeta) Assign(
	id string,
	address string,
	symbol string,
	name string,
	image string,
	decimals int,
	usdPrice float64,
	isNativeToken bool,
	extra map[string]string,
) {
	if isNativeToken {
		address = constant.NATIVE_TOKEN_ADDRESS
		if symbol == constant.SOLANA_NATIVE_TOKEN_SYMBOL {
			address = constant.SOLANA_NATIVETOKEN_TURNKEY_ADDRESS
		}
	}
	*t = TokenMeta{
		Id:       id,
		Address:  address,
		Symbol:   symbol,
		Name:     name,
		Image:    image,
		Decimals: decimals,
		UsdPrice: usdPrice,
		Extra:    extra,
	}
}
func (t *TokenMeta) key() string {
	return fmt.Sprintf("%s_%s", t.Symbol, t.Address)
}
