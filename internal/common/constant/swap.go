package constant

var TurnKeyNoEvmChainId = map[string]string{
	"mainnet-beta": "501424", // rango SOLANA
	"792703809":    "501424", // relay SOLANA
}

const (
	Rango = "rango"
	Relay = "relay"
)
const ArbitrumChainId = "42161"
const (
	USE_RANGO_ERRCODE          = "USE_RANGO"
	BALANCE_NOT_ENOUGH_ERRCODE = "BALANCE_NOT_ENOUGH"
	AMOUNT_TOO_LOW_ERRCODE     = "AMOUNT_TOO_LOW"
)

// rango Arbitrum constants
const (
	ARB_CHAIN_NAME    = "ARBITRUM"
	ARB_TOKEN_SYMBOL  = "ETH"
	ARB_TOKEN_ADDRESS = ""
)

// Relay
const (
	SOLANA_CHAIN_NAME_Relay = "solana"
	SOLANA_CHAIN_NAME_Rango = "SOLANA"
)
const (
	SOLANA_NATIVETOKEN_TURNKEY_ADDRESS = "So11111111111111111111111111111111111111112"
	NATIVE_TOKEN_ADDRESS               = ""
)
const SOLANA_NATIVE_TOKEN_SYMBOL = "SOL"
