package request

import (
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

type ReqTransaction struct {
	Oid           string `json:"oid"`
	BaseCoin      string `json:"baseCoin"`
	Side          string `json:"side"`
	Price         string `json:"price"`
	Size          string `json:"size"`
	OrderType     string `json:"orderType"`
	Operation     string `json:"operation"`
	OrderStatus   string `json:"orderStatus"`
	Cloid         string `json:"cloid"`
	AvgPx         string `json:"avgPx"`
	TotalSz       string `json:"totalSz"`
	Grouping      string `json:"grouping"`
	Created       string `json:"created"`
	WalletAddress string `json:"walletAddress"`
}

type ReqContractTxEvent struct {
	Cloid        string          `json:"cloid" binding:"required"`
	UserID       uuid.UUID       `json:"user_id" binding:"required"`
	UserAddress  string          `json:"user_address"`
	Side         string          `json:"side"`       // long/short or ask/bid
	OrderType    string          `json:"order_type"` // Market/Limit/Trigger
	Symbol       string          `json:"symbol"`     // "BTC-USDT-PERP"
	IsBuy        bool            `json:"is_buy"`
	Leverage     int             `json:"leverage"`
	Margin       decimal.Decimal `json:"margin"`
	IsMarket     bool            `json:"is_market"`
	TriggerPx    string          `json:"trigger_px"`
	Tpsl         string          `json:"tpsl"`
	Tif          string          `json:"tif"`   // Time In Force: Alo/Ioc/Gtc
	Base         string          `json:"base"`  // "BTC"
	Quote        string          `json:"quote"` // "USDC"
	Size         decimal.Decimal `json:"size"`
	Price        decimal.Decimal `json:"price"`
	AvgPrice     decimal.Decimal `json:"avg_price"`
	BuildFee     decimal.Decimal `json:"build_fee"`
	TotalFee     string          `json:"total_fee"`
	FeeBp        int             `json:"fee_bp"`
	BuildAddress string          `json:"build_address"`
	Status       string          `json:"status"`
	OID          int64           `json:"oid"`
	CreatedAt    string          `json:"created_at"`
	TotalSz      string          `json:"total_sz"`
	Hash         string          `json:"hash"`
	Asset        string          `json:"asset"`
	Coin         string          `json:"coin"`
	ReduceOnly   bool            `json:"reduce_only"`
	Grouping     string          `json:"grouping"`
	Operation    string          `json:"operation"`
}
