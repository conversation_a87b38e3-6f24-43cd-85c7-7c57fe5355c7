package response

type RespGetUserNodeTrades struct {
	Orders []OrderStatus `json:"orders"`
}
type NodeTrades struct {
	Coin             string          `json:"coin"`
	Side             string          `json:"side"`
	Time             string          `json:"time"`
	Px               string          `json:"px"`
	Sz               string          `json:"sz"`
	Hash             string          `json:"hash"`
	TradeDirOverride string          `json:"trade_dir_override"`
	SideInfoTrade    []SideInfoTrade `json:"side_info_trade"`
}

type SideInfoTrade struct {
	User     string  `json:"user"`
	StartPos string  `json:"start_pos"`
	Oid      int64   `json:"oid"`
	TwapID   int64  `json:"twap_id"`
	Cloid    string `json:"cloid"`
}