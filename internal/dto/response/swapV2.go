package response

import (
	"fmt"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/config"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/constant"
	"strconv"
	"strings"

	"github.com/gagliardetto/solana-go"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/relay"
)

type RespQuote struct {
	Items                 []StepItem `json:"items"`
	RequestId             string     `json:"requestId"`
	Type                  string     `json:"type"`
	Description           string     `json:"description"`
	OutPutAmountFormatted string     `json:"outPutAmountFormatted"`
	GasAmountFormatted    string     `json:"gasAmountFormatted"`
	ErrorCode             string     `json:"errorCode"`
	//relay
	GasTopupAmount          string `json:"amount"`
	GasTopupAmountFormatted string `json:"amountFormatted"`
	GasTopupAmountUsd       string `json:"amountUsd"`
	PlatformFeeAmountFormat string `json:"platformFeeAmountFormat"`
	PlatformFeeSymbol       string `json:"platformFeeSymbol"`
}

type StepItem struct {
	From                 string `json:"from"`
	To                   string `json:"to"`
	Data                 string `json:"data"`
	Value                string `json:"value"`
	ValueAmountUsd       string `json:"valueAmountUsd"`
	ValueAmountFormatted string `json:"valueAmountFormatted"`
	ChainId              string `json:"chainId"`
	Gas                  string `json:"gas"`
	GasAmountUsd         string `json:"gasAmountUsd"`
	GasAmountFormatted   string `json:"gasAmountFormatted"`
	MaxFeePerGas         string `json:"maxFeePerGas"`
	MaxPriorityFeePerGas string `json:"maxPriorityFeePerGas"`
	Nonce                string `json:"nonce"`
	BlockHash            string `json:"blockHash,omitempty"` // optional, used for solana transactions
	// no evm
	Instructions           *[]*Instruction `json:"instructions"`
	SerializedMessage      []int           `json:"serializedMessage"`
	IsApprovalTx           bool            `json:"isApprovalTx"`
	TransactionType        string          `json:"transactionType"`        // optional, used for evm transactions
	EstimatedTimeInSeconds int64           `json:"estimatedTimeInSeconds"` // optional, used for evm transactions

}
type Instruction struct {
	Keys      []InstructionKey `json:"keys"`
	ProgramId string           `json:"programId"`
	Data      string           `json:"data"`
}

type InstructionKey struct {
	Pubkey     string `json:"pubkey"`
	IsSigner   bool   `json:"isSigner"`
	IsWritable bool   `json:"isWritable"`
}

func (resp *RespQuote) Assign(
	data *relay.QuoteResponse, nonce uint64, blockHash *solana.Hash, originChain *model.RelayMetaChains) error {
	if len(data.Steps) == 0 || len(data.Steps[0].Items) == 0 {
		return fmt.Errorf("no steps or items in response")
	}
	var (
		items                 []StepItem
		blockHashStr          string
		outPutAmountFormatted string
	)
	if blockHash != nil {
		blockHashStr = blockHash.String()
	}
	if data.Details.TimeEstimate == 0 {
		data.Details.TimeEstimate = 5
	}
	for _, item := range data.Steps[0].Items {
		outPutAmountFormatted = data.Details.CurrencyOut.AmountFormatted
		instructions := utils.Translate[[]*Instruction](item.Data.Instructions)
		isApprovalTx := false
		if strings.HasPrefix(item.Data.Data, "0x095ea7b3") {
			isApprovalTx = true
		}
		_item := StepItem{
			From:                 item.Data.From,
			To:                   item.Data.To,
			Data:                 item.Data.Data,
			Value:                item.Data.Value,
			ValueAmountUsd:       data.Details.CurrencyOut.AmountUsd,
			ValueAmountFormatted: data.Details.CurrencyOut.AmountFormatted,
			ChainId:              originChain.ChainId,
			Gas:                  item.Data.Gas,
			GasAmountUsd:         data.Fees.Gas.AmountUsd,
			GasAmountFormatted:   data.Fees.Gas.AmountFormatted,
			MaxFeePerGas:         item.Data.MaxFeePerGas,
			MaxPriorityFeePerGas: item.Data.MaxPriorityFeePerGas,

			Nonce:                  fmt.Sprintf("%d", nonce),
			BlockHash:              blockHashStr,
			Instructions:           instructions,
			TransactionType:        originChain.Name,
			EstimatedTimeInSeconds: int64(data.Details.TimeEstimate),

			IsApprovalTx: isApprovalTx,
		}
		//solana specific fields
		if _item.From == "" {
			_item.From = data.Details.Sender
			_item.To = data.Details.Recipient

			_item.Value = data.Details.CurrencyOut.Amount
			_item.ValueAmountFormatted = data.Details.CurrencyOut.AmountFormatted
			_item.ValueAmountUsd = data.Details.CurrencyOut.AmountUsd
			_item.ChainId = originChain.ChainIdHex
			if v, ok := constant.TurnKeyNoEvmChainId[originChain.ChainId]; ok { //solana
				_item.ChainId = v
			}
		}
		items = append(items, _item)
	}

	*resp = RespQuote{
		Items:                   items,
		RequestId:               data.Steps[0].RequestId,
		OutPutAmountFormatted:   outPutAmountFormatted,
		GasAmountFormatted:      data.Fees.Gas.AmountFormatted,
		GasTopupAmount:          data.Details.CurrencyGasTopup.Amount,
		GasTopupAmountFormatted: data.Details.CurrencyGasTopup.AmountFormatted,
		GasTopupAmountUsd:       data.Details.CurrencyGasTopup.AmountUsd,
		//set platformFee
		PlatformFeeAmountFormat: data.Fees.Relayer.AmountFormatted,
		PlatformFeeSymbol:       data.Fees.Relayer.Currency.Symbol,
	}
	return nil
}

type Chain struct {
	ChainId    string `json:"chainId"`
	ChainImage string `json:"chainImage"`
	ChainName  string `json:"chainName"`

	Tokens []*TokenV2 `json:"tokens"`
}
type TokenV2 struct {
	Address    string           `json:"address"`
	Symbol     string           `json:"symbol"`
	Name       string           `json:"name"`
	Image      string           `json:"image"`
	Decimals   int              `json:"decimals"`
	UsdPrice   float64          `json:"usdPrice"`
	RelayExtra *RelayTokenExtra `json:"relayExtra,omitempty"`
	RangoExtra *RangoTokenExtra `json:"rangoExtra,omitempty"`
}
type RelayTokenExtra struct {
	Id string `json:"id"`
}

func (t *RelayTokenExtra) Assign(
	token *model.RelayMetaTokens) error {
	*t = RelayTokenExtra{
		Id: token.ID.String(),
	}
	return nil
}

type RangoTokenExtra struct {
	Id string `json:"id"`
}

func (t *RangoTokenExtra) Assign(
	token *model.RangoMetaTokens) error {
	*t = RangoTokenExtra{
		Id: token.ID.String(),
	}
	return nil
}

type RespExchangeMetaV2 struct {
	Chains []*Chain `json:"chains"`
}

func (r *RespExchangeMetaV2) Assign(
	metaData *config.MetaData,
) error {
	var chains []*Chain
	for _, chain := range metaData.Chains {
		var chainTokens []*TokenV2
		for _, token := range chain.Tokens {
			chainTokens = append(chainTokens, &TokenV2{
				Address:  token.Address,
				Symbol:   token.Symbol,
				Name:     token.Name,
				Image:    token.Image,
				Decimals: token.Decimals,
				UsdPrice: token.UsdPrice,
				RelayExtra: &RelayTokenExtra{
					token.Extra[constant.Relay],
				},
				RangoExtra: &RangoTokenExtra{
					token.Extra[constant.Rango],
				},
			})
		}
		chains = append(chains, &Chain{
			ChainId:    chain.ChainId,
			ChainImage: chain.ChainImage,
			ChainName:  chain.ChainName,
			Tokens:     chainTokens,
		})
	}
	*r = RespExchangeMetaV2{
		Chains: chains,
	}
	return nil
}

// DecimalToHexString converts a decimal string to a hex string with 0x prefix.
func DecimalToHexString(decimalStr string) string {
	if decimalStr == "" {
		return ""
	}
	v, err := strconv.ParseInt(decimalStr, 10, 64)
	if err != nil {
		return decimalStr
	}
	return "0x" + strconv.FormatInt(v, 16)
}
