package notification

import (
	"context"
	"encoding/json"
	"log"
	"math"
	"time"

	"github.com/nats-io/nats.go"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	natClient "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/nats"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
	"go.uber.org/zap"
)

const (
	significantChangeThreshold = 0.001 // 0.1% for rate limit
)

type PriceChangeDetectorService struct {
	natsClient     *natClient.NATSClient
	previousPrices map[string]float64
}

func NewPriceChangeDetectorService(client *natClient.NATSClient) *PriceChangeDetectorService {
	return &PriceChangeDetectorService{
		natsClient:     client,
		previousPrices: make(map[string]float64),
	}
}

func (s *PriceChangeDetectorService) Start(ctx context.Context) {
	global.GVA_LOG.Info("PriceChangeDetectorService subscribed", zap.String("subject", utils.RawPricesSubject))

	_, err := s.natsClient.AddStream(&nats.StreamConfig{
		Name:     utils.RawPricesStream,
		Subjects: []string{utils.RawPricesSubject},
		Storage:  nats.FileStorage,
		MaxAge:   48 * time.Hour,
	})
	if err != nil {
		global.GVA_LOG.Fatal("Failed to create user sync info stream", zap.Error(err))
	}

	sub, err := s.natsClient.SubscribeJS(utils.RawPricesSubject, func(msg *nats.Msg) {
		s.handleRawPriceMessage(ctx, msg)
	},
		nats.Durable(utils.RawPricesConsumer),
		nats.ManualAck(),
		nats.BindStream(utils.RawPricesStream),
		nats.AckWait(30*time.Second),
	)
	if err != nil {
		global.GVA_LOG.Fatal("PriceChangeDetectorService subscribe failed", zap.Error(err))
	}
	defer func() {
		global.GVA_LOG.Info("PriceChangeDetectorService subscribed", zap.String("subject", utils.RawPricesStream))
		sub.Unsubscribe()
	}()

	<-ctx.Done()
	global.GVA_LOG.Info("PriceChangeDetectorService cancelled", zap.String("subject", utils.RawPricesStream))
}

func (s *PriceChangeDetectorService) handleRawPriceMessage(ctx context.Context, msg *nats.Msg) {
	var rawPrice SymbolPriceRaw
	if err := json.Unmarshal(msg.Data, &rawPrice); err != nil {
		global.GVA_LOG.Error("PriceChangeDetectorService unmarshal raw price failed", zap.Error(err))
		msg.Ack()
		return
	}

	if _, ok := s.previousPrices[rawPrice.Symbol]; !ok {
		s.previousPrices[rawPrice.Symbol] = rawPrice.Price
	}

	prevPrice := s.previousPrices[rawPrice.Symbol]
	changePercent := (prevPrice - rawPrice.Price) / prevPrice
	if math.Abs(changePercent) < significantChangeThreshold {
		msg.Ack()
		return
	}
	direction := utils.NotificationDirectionUp
	if changePercent < 0 {
		direction = utils.NotificationDirectionDown
	}

	s.previousPrices[rawPrice.Symbol] = rawPrice.Price
	significantChangeEvent := SymbolPriceSignificantChange{
		Timestamp:        rawPrice.Timestamp,
		Symbol:           rawPrice.Symbol,
		CurrentPrice:     rawPrice.Price,
		PreviousPrice:    prevPrice,
		ChangeAmount:     rawPrice.Price - prevPrice,
		ChangePercent:    changePercent,
		ChangePercent24h: rawPrice.ChangePercent24h,
		Direction:        direction,
	}

	// Publish the significant change event
	eventData, err := json.Marshal(significantChangeEvent)
	if err != nil {
		log.Printf("PriceChangeDetector: Error marshaling significant change event: %v", err)
		msg.Ack()
		return
	}

	_, err = s.natsClient.PublishJS(utils.ProcessedPricesSubject, eventData)
	if err != nil {
		global.GVA_LOG.Error("PriceChangeDetector: Error publishing significant change event", zap.Error(err))
		msg.Ack()
		return
	}

	msg.Ack()
}
