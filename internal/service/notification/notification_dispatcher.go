package notification

import (
	"encoding/json"
	"time"

	"github.com/nats-io/nats.go"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	natsClient "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/nats"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
	"go.uber.org/zap"
	"golang.org/x/net/context"
)

const (
	repeatedNotificationThrottle = 1 * time.Hour
)

type NotificationDispatcherService struct {
	natsSubscribersClient *natsClient.NATSClient
	symbolRepo            repo.SymbolRepo
	natPublisherClient    *natsClient.NATSClient
}

func NewNotificationDispatcherService(subscriber *natsClient.NATSClient, publisher *natsClient.NATSClient, symbolRepo repo.SymbolRepo) *NotificationDispatcherService {
	return &NotificationDispatcherService{
		natsSubscribersClient: subscriber,
		natPublisherClient:    publisher,
		symbolRepo:            symbolRepo,
	}
}

func (s *NotificationDispatcherService) Start(ctx context.Context) {
	global.GVA_LOG.Info("NotificationDispatcherService subscribed", zap.String("subject", utils.ProcessedPricesSubject))

	_, err := s.natsSubscribersClient.AddStream(&nats.StreamConfig{
		Name:     utils.ProcessedPricesStream,
		Subjects: []string{utils.ProcessedPricesSubject},
		Storage:  nats.FileStorage,
		MaxAge:   48 * time.Hour,
	})
	if err != nil {
		global.GVA_LOG.Fatal("Failed to create user sync info stream", zap.Error(err))
	}

	sub, err := s.natsSubscribersClient.SubscribeJS(utils.ProcessedPricesSubject, func(msg *nats.Msg) {
		s.handleSignificantPriceChange(ctx, msg)
	},
		nats.Durable(utils.ProcessedPricesConsumer),
		nats.ManualAck(),
		nats.BindStream(utils.ProcessedPricesStream),
		nats.AckWait(30*time.Second),
	)
	if err != nil {
		global.GVA_LOG.Fatal("NotificationRuleEngine subscribe failed", zap.Error(err))
	}
	defer func() {
		global.GVA_LOG.Info("NotificationRuleEngine subscribed", zap.String("subject", utils.ProcessedPricesSubject))
		sub.Unsubscribe()
	}()

	<-ctx.Done()
	global.GVA_LOG.Info("NotificationRuleEngine cancelled", zap.String("subject", utils.ProcessedPricesSubject))
}

func (s *NotificationDispatcherService) handleSignificantPriceChange(ctx context.Context, msg *nats.Msg) {
	var event SymbolPriceSignificantChange
	if err := json.Unmarshal(msg.Data, &event); err != nil {
		global.GVA_LOG.Error("handleSignificantPriceChange", zap.Error(err))
		msg.Ack()
		return
	}

	settings, err := s.symbolRepo.GetActiveSettingsBySymbol(ctx, event.Symbol)
	if err != nil {
		global.GVA_LOG.Error("NotificationRuleEngine: Error fetching settings for", zap.Error(err))
		msg.Ack()
		return
	}

	for _, setting := range settings {
		// 1. Check if the price change matches the trigger condition
		shouldTrigger := s.evaluateCondition(*setting, event)
		if !shouldTrigger {
			continue
		}

		// 2. Check throttling for "repeated" notifications
		if setting.IsReminderOnce == false {
			if time.Since(setting.LastNotifiedAt) < repeatedNotificationThrottle {
				continue
			}
		}

		dispatchReq := SymbolAlertEvent{
			NotificationSettingID: setting.ID.String(),
			UserID:                setting.UserID.String(),
			Symbol:                setting.Symbol,
			Type:                  setting.Type,
			Timestamp:             time.Now(),
			CurrentPrice:          event.CurrentPrice,
			PreviousPrice:         event.PreviousPrice,
			ChangePercent24h:      event.ChangePercent24h,
			ChangeDirection:       event.Direction,
		}

		reqData, err := json.Marshal(dispatchReq)
		if err != nil {
			global.GVA_LOG.Error("handleSignificantPriceChange", zap.Error(err))
			continue
		}

		_, err = s.natPublisherClient.PublishJS(utils.NotificationDispatchSubject, reqData)
		if err != nil {
			global.GVA_LOG.Error("handleSignificantPriceChange: Failed to publish dispatch request for setting", zap.Error(err))
			continue
		}

		if setting.IsReminderOnce {
			if err := s.symbolRepo.UpdateAlertSymbolSetting(model.UserNotificationSetting{
				ID:       setting.ID,
				IsActive: false,
			}); err != nil {
				global.GVA_LOG.Error("handleSignificantPriceChange: Failed to update alert setting", zap.Error(err))
			}
		} else {
			if err := s.symbolRepo.MarkNotifiedSetting(model.UserNotificationSetting{
				ID:             setting.ID,
				LastNotifiedAt: time.Now(),
			}); err != nil {
				global.GVA_LOG.Error("handleSignificantPriceChange: Failed to update alert setting", zap.Error(err))
			}
		}
	}

	msg.Ack()
}

func (s *NotificationDispatcherService) evaluateCondition(setting model.UserNotificationSetting, event SymbolPriceSignificantChange) bool {
	if setting.Direction != event.Direction {
		return false
	}

	switch setting.Type {
	case utils.NotificationTypePrice:
		if (setting.Direction == utils.NotificationDirectionUp && event.CurrentPrice >= setting.Value) ||
			(setting.Direction == utils.NotificationDirectionDown && event.CurrentPrice <= setting.Value) {
			return true
		}
		return false
	case utils.NotificationTypePercent:
		if (setting.Direction == utils.NotificationDirectionUp && event.ChangePercent24h >= setting.Value) ||
			(setting.Direction == utils.NotificationDirectionDown && event.ChangePercent24h <= setting.Value) {
			return true
		}
		return false
	default:
		return false
	}
}
