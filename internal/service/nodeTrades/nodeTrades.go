package nodetrades

import (
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/request"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/response"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service"
)

type NodeTradesService struct {
	nodeTradesRepo repo.NodeTradesRepo
}

func NewNodeTradesService(nodeTradesRepo repo.NodeTradesRepo) service.NodeTradesI {
	return &NodeTradesService{
		nodeTradesRepo: nodeTradesRepo,
	}
}

func (s *NodeTradesService) GetNodeTrades(req request.ReqGetUserNodeTrades) (*response.RespGetUserNodeTrades, error) {
	trades, err := s.nodeTradesRepo.GetUserNodeTrades(req.UserID, req.LastID, req.Limit)
	if err != nil {
		return nil, err
	}

	respOpenTrades := make([]response.NodeTrades, 0)
	for _, trade := range trades {
		respOpenTrades = append(respOpenTrades, response.NodeTrades{
			Coin:             trade.Coin,
			Side:             "",
			Time:             "",
			Px:               "",
			Sz:               "",
			Hash:             "",
			TradeDirOverride: "",
			SideInfoTrade:    []response.SideInfoTrade{},
		})
	}

	return &response.RespGetUserNodeTrades{}, nil
}
