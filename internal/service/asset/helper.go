package asset

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/cache"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/nats"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/hyperliquid"
	"time"
)

func AssignUserPosition(userID uuid.UUID, walletAddress string, accountSummary hyperliquid.AccountSummary) []*model.Position {
	var (
		positions = make([]*model.Position, 0)
	)
	for _, pos := range accountSummary.AssetPositions {
		var (
			side int
		)

		id, _ := uuid.NewV7()
		size := utils.StringToFloat(pos.Position.Szi)
		entryPx := utils.StringToFloat(pos.Position.EntryPx)
		positionValue := utils.StringToFloat(pos.Position.PositionValue)
		unrealizePNL := utils.StringToFloat(pos.Position.UnrealizedPnl)
		if (unrealizePNL / (positionValue - entryPx*size)) > 0 {
			side = 1
		} else {
			side = -1
		}

		positions = append(positions, &model.Position{
			ID:                 id,
			Symbol:             pos.Position.Coin,
			Type:               pos.Type,
			UpdateAt:           time.Now(),
			UserID:             userID,
			WalletAddress:      walletAddress,
			Size:               size,
			EntryPx:            entryPx,
			Side:               side,
			MarginMode:         pos.Position.Leverage.Type,
			FundingAllTime:     utils.StringToFloat(pos.Position.CumFunding.AllTime),
			FundingSinceChange: utils.StringToFloat(pos.Position.CumFunding.SinceChange),
		})

	}

	return positions
}

func AssignUserBalanceToMsg(userID uuid.UUID, walletAddress string, balance float64) (nats.UserBalanceEventMsg, error) {
	var (
		ARBStatisticData model.CoinStatistic
		cacheKey         = fmt.Sprintf("%s%s", cache.KeySymbolStatisticData(), utils.NativeToken)
	)

	str, err := global.GVA_REDIS.Get(context.Background(), cacheKey).Result()
	if err == nil && str != "" {
		if err = json.Unmarshal([]byte(str), &ARBStatisticData); err != nil {
			return nats.UserBalanceEventMsg{}, fmt.Errorf("failed to unmarshal symbol detail data: %w", err)
		}
	}

	ts := time.Now()

	userBalanceDataMsg := make([]nats.UserBalanceData, 0)
	userBalanceDataMsg = append(userBalanceDataMsg, nats.UserBalanceData{
		UserID:             userID.String(),
		Timestamp:          ts.Unix(),
		WalletAddress:      walletAddress,
		WalletType:         utils.WalletType,
		NativeTokenSymbol:  utils.NativeToken,
		NativeTokenAddress: utils.NativeTokenAddress,
		NativeTokenBalance: balance / ARBStatisticData.MarkPx,
		USDBalance:         balance,
	})

	return nats.UserBalanceEventMsg{
		Events: userBalanceDataMsg,
	}, nil
}
