package asset

import (
	"context"
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"github.com/nats-io/nats.go"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	clientNATS "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/nats"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/hyperliquid"
	"go.uber.org/zap"
)

type User struct {
	assetRepo  repo.AssetRepo
	tradeRepo  repo.TradeRepo
	natsClient *clientNATS.NATSClient
}

type UserWallet struct {
	ID              string    `json:"id"`
	UserID          string    `json:"userId"`
	WalletAddress   string    `json:"walletAddress"`
	WalletID        string    `json:"walletID"`
	WalletAccountID string    `json:"walletAccountId"`
	CreatedAt       time.Time `json:"createdAt"`
}

type UserWalletMsg struct {
	UserWallets []UserWallet `json:"userWallets"`
}

func NewUserService(assetRepo repo.AssetRepo, tradeRepo repo.TradeRepo, client *clientNATS.NATSClient) *User {
	return &User{
		natsClient: client,
		assetRepo:  assetRepo,
		tradeRepo:  tradeRepo,
	}
}

func (s *User) Start(ctx context.Context) {
	global.GVA_LOG.Info("UserService subscribed", zap.String("subject", utils.UserSyncInfoSubject))

	// readonly steam, can't create or update
	// _, err := s.natsClient.AddStream(&nats.StreamConfig{
	// 	Name:     utils.UserSyncInfoStream,
	// 	Subjects: []string{utils.UserSyncInfoSubject},
	// 	Storage:  nats.FileStorage,
	// })
	// if err != nil {
	// 	global.GVA_LOG.Fatal("Failed to create user sync info stream", zap.Error(err))
	// }

	sub, err := s.natsClient.SubscribeJS(utils.UserSyncInfoSubject, func(msg *nats.Msg) {
		s.HandleUserSyncMessage(ctx, msg) // Pass context to handler
	},
		nats.Durable(utils.UserSyncInfoConsumer),
		nats.ManualAck(),
		nats.BindStream(utils.UserSyncInfoStream),
		nats.SkipConsumerLookup(),
		nats.AckWait(30*time.Second), // How long NATS waits for an ACK before redelivering
	)

	if err != nil {
		global.GVA_LOG.Error("User Wallet worker start failed", zap.Error(err))
	}
	defer func() {
		global.GVA_LOG.Info("User Wallet worker Unsubscribe", zap.String("subject", utils.UserSyncInfoSubject))
		sub.Unsubscribe()
	}()

	<-ctx.Done()
}

func (s *User) HandleUserSyncMessage(ctx context.Context, msg *nats.Msg) {
	var userWalletMsg UserWalletMsg
	err := json.Unmarshal(msg.Data, &userWalletMsg)
	if err != nil {
		global.GVA_LOG.Error("User Wallet worker unmarshal failed", zap.Error(err))
		msg.Ack()
		return
	}

	for _, userWallet := range userWalletMsg.UserWallets {
		userID := uuid.MustParse(userWallet.UserID)
		s.assetRepo.UpsertUserWallet(model.UserWallet{
			ID:              uuid.MustParse(userWallet.ID),
			UserID:          userID,
			WalletAddress:   userWallet.WalletAddress,
			CreatedAt:       userWallet.CreatedAt,
			WalletID:        uuid.MustParse(userWallet.WalletID),
			WalletAccountID: uuid.MustParse(userWallet.WalletAccountID),
		})

		accountSummary, err := hyperliquid.GetAccountSummaryFromAPI(userWallet.WalletAddress)
		if err != nil {
			global.GVA_LOG.Error("User Wallet worker get account summary failed", zap.Error(err))
			continue
		}

		userPositions := AssignUserPosition(userID, userWallet.WalletAddress, accountSummary)
		if err = s.tradeRepo.UpdateUserPosition(userPositions); err != nil {
			global.GVA_LOG.Error("User Wallet worker update user positions failed", zap.Error(err))
			continue
		}

		accountBalance := utils.StringToFloat(accountSummary.CrossMarginSummary.AccountValue)
		userBalanceEvent, err := AssignUserBalanceToMsg(userID, userWallet.WalletAddress, accountBalance)

		data, _ := json.Marshal(userBalanceEvent)

		_, err = s.natsClient.PublishJS(utils.WalletBalanceSubject, data)
		if err != nil {
			global.GVA_LOG.Error("[task] TrackUserBalances ", zap.Any("err", err))
		}
	}

	if err != nil {
		global.GVA_LOG.Error("User Wallet worker add failed", zap.Error(err))
		msg.Ack()
		return
	}

	msg.Ack()
}
