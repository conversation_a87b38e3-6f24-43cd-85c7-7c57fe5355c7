package symbol

import (
	"encoding/json"
	"fmt"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/cache"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/request"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/response"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service"
	symbolModel "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/symbol"
	"golang.org/x/net/context"
	"math"
	"strings"
	"time"
)

type SymbolService struct {
	symbolRepo repo.SymbolRepo
}

func NewSymbolService(symbolRepo repo.SymbolRepo) service.SymbolI {
	return &SymbolService{
		symbolRepo: symbolRepo,
	}
}

func (s *SymbolService) GetOHLC(req request.ReqOHLC) (*response.RespOHLC, error) {
	klines, err := s.symbolRepo.GetOHLC(req.Symbol, req.Interval, req.Timestamp, req.IsForward, req.Limit)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch routes: %v", err)
	}

	if len(klines) == 0 {
		return &response.RespOHLC{
			Symbol: req.Symbol,
			Data:   []response.OHLC{},
		}, nil
	}

	// convert to response format
	respOHLCs := make([]response.OHLC, 0, len(klines))
	for _, kline := range klines {
		respOHLCs = append(respOHLCs, response.OHLC{
			Open:      kline.Open,
			High:      kline.High,
			Low:       kline.Low,
			Close:     kline.Close,
			Timestamp: kline.TimeOpen.UnixMilli(),
			Volume:    kline.Volume,
		})
	}
	return &response.RespOHLC{
		Symbol: req.Symbol,
		Data:   respOHLCs,
	}, nil
}

func (s *SymbolService) GetSymbolDetail(req request.ReqGetSymbolDetail) (*response.RespGetSymbolDetail, error) {
	var (
		overallData   *model.Coin
		statisticData *model.CoinStatistic
	)

	cacheKey := fmt.Sprintf("%s%s", cache.KeySymbolOverallData(), req.Symbol)
	str, err := global.GVA_REDIS.Get(context.Background(), cacheKey).Result()
	if err == nil && str != "" {
		if err = json.Unmarshal([]byte(str), &overallData); err != nil {
			return nil, fmt.Errorf("failed to unmarshal symbol detail data: %w", err)
		}
	} else {
		overallData, err = s.symbolRepo.GetSymbolOverallData(req.Symbol)
		if err != nil {
			return nil, fmt.Errorf("failed to get symbol detail: %v", err)
		}
	}

	cacheKey = fmt.Sprintf("%s%s", cache.KeySymbolStatisticData(), req.Symbol)
	str, err = global.GVA_REDIS.Get(context.Background(), cacheKey).Result()
	if err == nil && str != "" {
		if err = json.Unmarshal([]byte(str), &statisticData); err != nil {
			return nil, fmt.Errorf("failed to unmarshal symbol detail data: %w", err)
		}
	} else {
		statisticData, err = s.symbolRepo.GetSymbolStatistic(req.Symbol)
		if err != nil {
			return nil, fmt.Errorf("failed to get symbol detail: %v", err)
		}
	}

	maxPrice, minPrice, err := s.symbolRepo.GetSymbolOneDayRange(req.Symbol)
	if err != nil {
		return nil, fmt.Errorf("failed to get symbol detail: %v", err)
	}

	s.symbolRepo.UpdatePopularSymbol(req.Symbol)

	return &response.RespGetSymbolDetail{
		Symbol:          req.Symbol,
		MaxLeverage:     overallData.MaxLeverage,
		OnlyIsolated:    overallData.OnlyIsolated,
		SizeDecimals:    overallData.SzDecimals,
		MarginTableID:   overallData.MarginTableID,
		MarketCap:       statisticData.MarketCap,
		High:            math.Max(statisticData.MarkPx, maxPrice),
		Low:             math.Min(statisticData.MarkPx, minPrice),
		Volume:          statisticData.DayNtlVlm,
		ChangePxPercent: statisticData.ChangePxPercent,
		CurrentPrice:    statisticData.MarkPx,
	}, nil
}

func (s *SymbolService) GetSymbolList(req request.ReqSymbolList) (*response.RespSymbolList, error) {
	resp, err := s.getSymbolListByCategory(context.Background(), req.Condition, req.Category)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch symbol list: %v", err)
	}

	symbolList := make([]response.Symbol, 0, len(resp))
	for _, symbol := range resp {
		symbolList = append(symbolList, response.Symbol{
			Symbol:         symbol.Symbol,
			MarketCap:      symbol.MarketCap,
			ChangPxPercent: symbol.ChangePxPercent,
			OpenInterest:   symbol.OpenInterest,
			Volume:         symbol.DayNtlVlm,
			CurrentPrice:   symbol.MarkPx,
			MaxLeverage:    symbol.MaxLeverage,
		})
	}

	return &response.RespSymbolList{
		List: symbolList,
	}, nil
}

func (s *SymbolService) UpsertFavoriteSymbol(req request.ReqUpsertFavoriteSymbol) (*response.RespUpsertFavoriteSymbol, error) {
	var failList = make([]string, 0)
	for _, symbol := range req.Symbol {
		resp, err := s.symbolRepo.UpsertFavoriteSymbol(req.UserID, symbol, req.IsFavorite)
		if err != nil {
			return nil, fmt.Errorf("create transaction UpsertFavoriteSymbol failed: %v", err)
		}

		if resp.UserID != req.UserID || resp.Symbol != symbol || resp.IsFavorite != req.IsFavorite {
			failList = append(failList, resp.Symbol)
		}
	}

	if len(failList) > 0 {
		return nil, fmt.Errorf("failed to update favorite symbols: %v", failList)
	}

	upsertResp := &response.RespUpsertFavoriteSymbol{
		Status: "success",
	}

	return upsertResp, nil
}

func (s *SymbolService) GetFavoriteSymbols(req request.ReqGetFavoriteSymbols) (*response.RespGetFavoriteSymbols, error) {
	favSymbolList, err := s.symbolRepo.GetFavoriteSymbols(req.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch symbol list: %v", err)
	}

	symbolsData, err := s.getSymbolListByCategory(context.Background(), "volume", "")
	if err != nil {
		return nil, fmt.Errorf("failed to fetch symbol list: %v", err)
	}

	symbolList := s.AssignFavoriteSymbol(favSymbolList, symbolsData)
	return &response.RespGetFavoriteSymbols{
		List: symbolList,
	}, nil
}

func (s *SymbolService) AssignFavoriteSymbol(favSymbolList []*model.UserCoin, symbolData []*symbolModel.SymbolStatistic) []response.Symbol {
	var (
		m    = make(map[string]interface{})
		nilI interface{}
	)
	for _, favSymbol := range favSymbolList {
		m[favSymbol.Symbol] = nilI
	}

	var favoriteSymbolData = make([]*symbolModel.SymbolStatistic, 0)
	for _, data := range symbolData {
		if _, ok := m[data.Symbol]; ok {
			favoriteSymbolData = append(favoriteSymbolData, data)
		}
	}

	symbolList := make([]response.Symbol, 0, len(favSymbolList))
	for _, symbol := range favoriteSymbolData {
		symbolList = append(symbolList, response.Symbol{
			Symbol:         symbol.Symbol,
			MarketCap:      symbol.MarketCap,
			ChangPxPercent: symbol.ChangePxPercent,
			Volume:         symbol.DayNtlVlm,
			CurrentPrice:   symbol.MarkPx,
			MaxLeverage:    symbol.MaxLeverage,
		})
	}

	return symbolList
}

func (s *SymbolService) GetUserSymbolPreference(req request.GetUserSymbolPreferenceDto) (*response.RespUserSymbolPreference, error) {
	userSymbol, err := s.symbolRepo.GetUserSymbolDetail(req.UserID, req.Symbol)
	if err != nil {
		return nil, fmt.Errorf("failed to get user symbol detail: %v", err)
	}

	if userSymbol == nil {
		coin, err := s.symbolRepo.GetSymbolOverallData(req.Symbol)
		if coin == nil || err != nil {
			return nil, fmt.Errorf("coin not found")
		}

		return &response.RespUserSymbolPreference{
			Symbol:     req.Symbol,
			IsFavorite: false,
			IsCross:    true,
			Leverage:   coin.MaxLeverage,
		}, nil
	}

	leverage := userSymbol.Leverage

	if leverage == nil {
		coin, err := s.symbolRepo.GetSymbolOverallData(req.Symbol)
		if coin == nil || err != nil {
			return nil, fmt.Errorf("coin not found")
		}

		leverage = &coin.MaxLeverage
	}

	return &response.RespUserSymbolPreference{
		Symbol:          req.Symbol,
		IsFavorite:      userSymbol.IsFavorite,
		IsCross:         userSymbol.IsCross,
		Leverage:        *leverage,
		OrderUnitInBase: userSymbol.OrderUnitInBase,
		TPSLUnit:        userSymbol.TPSLUnit,
	}, nil
}

func (s *SymbolService) UpsertUserSymbolPreference(req request.UpsertUserSymbolPreferenceDto) (*response.RespUserSymbolPreference, error) {
	var (
		userSymbol *model.UserCoin
		err        error
	)

	fieldUpsert := make(map[string]interface{})
	if req.IsCross != nil {
		fieldUpsert["is_cross"] = *req.IsCross
	}

	if req.Leverage != nil {
		fieldUpsert["leverage"] = *req.Leverage
	} else {
		coin, err := s.symbolRepo.GetSymbolOverallData(req.Symbol)
		if coin == nil || err != nil {
			return nil, fmt.Errorf("coin not found")
		}

		fieldUpsert["leverage"] = coin.MaxLeverage
	}

	if req.TPSLUnit != nil {
		fieldUpsert["tpsl_unit"] = *req.TPSLUnit
	}

	if req.OrderUnitInBase != nil {
		fieldUpsert["order_unit_in_base"] = *req.OrderUnitInBase
	}
	fieldUpsert["updated_at"] = time.Now()

	userSymbol, err = s.symbolRepo.UpsertUserSymbolPreference(req.UserID, req.Symbol, fieldUpsert)
	if err != nil {
		return nil, fmt.Errorf("failed to upsert user symbol preference: %v", err)
	}

	return &response.RespUserSymbolPreference{
		Symbol:          userSymbol.Symbol,
		IsFavorite:      userSymbol.IsFavorite,
		IsCross:         userSymbol.IsCross,
		Leverage:        *userSymbol.Leverage,
		OrderUnitInBase: userSymbol.OrderUnitInBase,
		TPSLUnit:        userSymbol.TPSLUnit,
	}, nil
}

func (s *SymbolService) GetCategory() (*response.RespGetCategory, error) {
	// Todo: sort category like hyperliquid
	categories, err := s.symbolRepo.GetCategory()
	if err != nil {
		return nil, fmt.Errorf("failed to fetch category: %v", err)
	}

	return &response.RespGetCategory{
		Categories: categories,
	}, nil
}

func (s *SymbolService) SearchSymbol(filter string) (*response.RespSearchSymbol, error) {
	resp, err := s.getSymbolListByCategory(context.Background(), "volume", "")
	if err != nil {
		return nil, fmt.Errorf("failed to fetch symbol list: %v", err)
	}

	// Todo: Query to database

	symbolList := make([]response.Symbol, 0)
	for _, symbol := range resp {
		if !strings.Contains(strings.ToUpper(symbol.Symbol), strings.ToUpper(filter)) {
			continue
		}

		symbolList = append(symbolList, response.Symbol{
			Symbol:         symbol.Symbol,
			MarketCap:      symbol.MarketCap,
			ChangPxPercent: symbol.ChangePxPercent,
			OpenInterest:   symbol.OpenInterest,
			Volume:         symbol.DayNtlVlm,
			CurrentPrice:   symbol.MarkPx,
			MaxLeverage:    symbol.MaxLeverage,
		})

		s.symbolRepo.UpdatePopularSymbol(symbol.Symbol)
	}

	return &response.RespSearchSymbol{
		List: symbolList,
	}, nil
}

func (s *SymbolService) GetNewSymbol() (*response.RespGetNewSymbol, error) {
	resp, err := s.symbolRepo.GetNewSymbolFromCache()
	if err != nil || resp == nil {
		// Get from db
		resp, err = s.symbolRepo.GetNewSymbolFromDB()
		if err != nil {
			return nil, fmt.Errorf("failed to fetch new symbol: %v", err)
		}

		s.symbolRepo.CacheNewSymbol(resp)
	}

	return &response.RespGetNewSymbol{
		List: resp,
	}, nil
}

func (s *SymbolService) GetPopularSymbol(number int) (*response.RespGetPopularSymbol, error) {
	resp, err := s.symbolRepo.GetPopularSymbol(int64(number))
	if err != nil {
		return nil, fmt.Errorf("failed to fetch popular symbol: %v", err)
	}

	list := make([]response.Symbol, 0)
	for _, redisZ := range resp {
		symbol := redisZ.Member.(string)

		var statisticData *model.CoinStatistic
		cacheKey := fmt.Sprintf("%s%s", cache.KeySymbolStatisticData(), symbol)
		str, err := global.GVA_REDIS.Get(context.Background(), cacheKey).Result()
		if err == nil && str != "" {
			if err = json.Unmarshal([]byte(str), &statisticData); err != nil {
				return nil, fmt.Errorf("failed to unmarshal symbol detail data: %w", err)
			}
		} else {
			statisticData, err = s.symbolRepo.GetSymbolStatistic(symbol)
			if err != nil {
				return nil, fmt.Errorf("failed to get symbol detail: %v", err)
			}
		}

		list = append(list, response.Symbol{
			Symbol:         symbol,
			MarketCap:      statisticData.MarketCap,
			ChangPxPercent: statisticData.ChangePxPercent,
			OpenInterest:   statisticData.OpenInterest,
			Volume:         statisticData.DayNtlVlm,
			CurrentPrice:   statisticData.MarkPx,
		})
	}

	return &response.RespGetPopularSymbol{
		List: list,
	}, nil
}

func (s *SymbolService) GetSymbolSignal(req request.ReqSignals) (*response.RespSignals, error) {
	signals, total, err := s.symbolRepo.GetSignal(req.User, req.Limit, req.Offset)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch popular symbol: %v", err)
	}
	var resp response.RespSignals
	resp.Total = total
	resp.Signals = make([]response.Signal, 0, len(signals))
	for _, signal := range signals {
		var respSignal response.Signal
		respSignal.Assign(signal)
		resp.Signals = append(resp.Signals, respSignal)
	}
	return &resp, nil
}

func (s *SymbolService) getCategoryInfo(condition string, category string) (string, string, string, error) {
	var (
		cacheKey       string
		orderClause    string
		whereCondition string
	)

	switch condition {
	case "volume":
		cacheKey = cache.KeySymbolListByVolume()
		orderClause = "coin_statistic.day_ntl_vlm DESC"
	case "gainer":
		cacheKey = cache.KeySymbolListByGainer()
		orderClause = "coin_statistic.change_px_percent DESC"
	case "loser":
		cacheKey = cache.KeySymbolListByLoser()
		orderClause = "coin_statistic.change_px_percent ASC"
	case "trend":
		cacheKey = cache.KeySymbolListByTrend()
		orderClause = "coin_statistic.day_ntl_vlm DESC"
		whereCondition = "coin_statistic.change_px_percent > 50"
	case "marketCap":
		cacheKey = cache.KeySymbolListByMarketCap()
		orderClause = "coin_statistic.market_cap DESC"
	case "openInterest":
		cacheKey = cache.KeySymbolListByOpenInterest()
		orderClause = "coin_statistic.open_interest DESC"
	case "category":
		cacheKey = cache.KeySymbolListByCategory() + category
	default:
		return cacheKey, orderClause, whereCondition, fmt.Errorf("symbol list not supported")
	}

	return cacheKey, orderClause, whereCondition, nil
}

func (s *SymbolService) getSymbolListByCategory(ctx context.Context, condition string, category string) ([]*symbolModel.SymbolStatistic, error) {
	var (
		list     []*symbolModel.SymbolStatistic
		coinData []*model.CoinData
	)
	cacheKey, orderClause, whereCondition, err := s.getCategoryInfo(condition, category)
	if err != nil {
		return nil, err
	}

	str, err := global.GVA_REDIS.Get(ctx, cacheKey).Result()
	if err == nil && str != "" {
		if err = json.Unmarshal([]byte(str), &list); err != nil {
			return nil, err
		}
	} else {
		if condition == "category" {
			coinData, err = s.symbolRepo.GetSymbolDataByCategory(category)
		} else {
			coinData, err = s.symbolRepo.GetSymbolsData(whereCondition, orderClause)
		}
		for _, data := range coinData {
			list = append(list, &symbolModel.SymbolStatistic{
				Symbol:            data.Coin.Symbol,
				MaxLeverage:       data.MaxLeverage,
				Funding:           data.Funding,
				MarketCap:         data.MarketCap,
				TotalSupply:       data.TotalSupply,
				CirculatingSupply: data.CirculatingSupply,
				OpenInterest:      data.OpenInterest,
				PrevDayPx:         data.PrevDayPx,
				DayNtlVlm:         data.DayNtlVlm,
				MarkPx:            data.MarkPx,
				ChangePx:          data.ChangePx,
				ChangePxPercent:   data.ChangePxPercent,
			})
		}
		if err != nil {
			return nil, err
		}

		global.GVA_REDIS.Set(ctx, cacheKey, list, time.Hour)
	}

	return list, nil
}
