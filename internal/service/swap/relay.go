package swap

import (
	"errors"
	"fmt"
	"github.com/gagliardetto/solana-go"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/config"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/constant"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/request"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/response"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/chainHelper"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/rango"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/relay"
	"math/big"
	"strconv"
)

func (s *Swap) GetMetaData() (*config.MetaData, error) {
	chains, err := s.aRepo.SwapMetaData()
	if err != nil {
		return nil, fmt.Errorf("failed to get swap meta data: %v", err)
	}
	return chains, nil
}

// GetRelayQuote 通过 uuid 查询 token/chain 信息后组装 relay.QuoteRequest
func (s *Swap) GetRelayQuote(req request.ReqGetQuote) (*response.RespQuote, error) {
	//fmt.Println(time.Now().UnixMilli())
	// 查找 origin token/chain
	originToken, err := s.repo.GetRelayTokenByUUID(req.OriginId)
	if err != nil {
		return nil, fmt.Errorf("failed to get relay quote: %v", err)
	}
	originChain, err := s.repo.GetRelayChainById(originToken.ChainId)
	if err != nil {
		return nil, fmt.Errorf("failed to get relay chain: %v", err)
	}
	// 查找 destination token/chain
	destinationToken, err := s.repo.GetRelayTokenByUUID(req.DestinationId)
	if err != nil {
		return nil, fmt.Errorf("failed to get relay quote token: %v", err)
	}
	destinationChain, err := s.repo.GetRelayChainById(destinationToken.ChainId)
	if err != nil {
		return nil, fmt.Errorf("failed to get relay chain: %v", err)
	}
	// 组装参数
	originChainIdInt, err := strconv.ParseInt(originChain.ChainId, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("failed to parse origin chain id: %v", err)
	}
	destinationChainIdInt, err := strconv.ParseInt(destinationChain.ChainId, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("failed to parse destination chain id: %v", err)
	}
	arbEthToken, err := s.repo.GetToken(constant.ARB_CHAIN_NAME, constant.ARB_TOKEN_SYMBOL, constant.ARB_TOKEN_ADDRESS)
	if err != nil {
		return nil, fmt.Errorf("failed to get arb token: %v", err)
	}
	//fmt.Println(time.Now().UnixMilli())
	solverAndReceiver, err := relay.GetConfig(destinationChainIdInt, destinationChainIdInt, req.Recipient, destinationToken.TokenId)
	if err != nil {
		return nil, fmt.Errorf("failed to get relay config: %v", err)
	}
	topupGas := false
	if solverAndReceiver.User.Balance == "" {
		topupGas = true
	}
	if !destinationToken.IsNative && destinationToken.ChainId == constant.ArbitrumChainId {
		userBalance, _ := big.NewInt(0).SetString(solverAndReceiver.User.Balance, 10)
		thresholdValue := big.NewInt(1000000000000000) //<0.001 ETH use topupGas
		if userBalance.Cmp(thresholdValue) < 0 {
			topupGas = true
		}
	}
	//fmt.Println(time.Now().UnixMilli())
	quote, err := relay.GetRelayQuote(&relay.QuoteRequest{
		User:                 req.User,
		OriginChainId:        int(originChainIdInt),
		DestinationChainId:   int(destinationChainIdInt),
		OriginCurrency:       originToken.Address,
		DestinationCurrency:  destinationToken.Address,
		Recipient:            req.Recipient,
		Amount:               req.Amount,
		TradeType:            "EXACT_INPUT",
		Referrer:             "relay.link",
		UseExternalLiquidity: false,
		UseDepositAddress:    false,
		TopupGas:             topupGas,
	})
	if err != nil {
		resp, err := RespQuoteErrorCode(err, "", err.Error())
		if err != nil {
			return nil, fmt.Errorf("[getRelayQuote] error=: %v", err)
		}
		return resp, nil
	}
	//fmt.Println(time.Now().UnixMilli())
	var (
		resp       response.RespQuote
		solanaHash solana.Hash
		nonce      uint64
	)

	if len(quote.Steps) > 0 && len(quote.Steps[0].Items) > 0 {

		ok, price, err := IsExceedCapacity(quote.Details.CurrencyOut.Amount,
			solverAndReceiver.Solver.CapacityPerRequest, arbEthToken.UsdPrice)
		if err != nil {
			return nil, fmt.Errorf("failed to compare value: %v", err)
		}
		if ok {
			respErr, err := RespQuoteErrorCode(errors.New(constant.USE_RANGO_ERRCODE), constant.Rango, price)
			if err != nil {
				return nil, fmt.Errorf("[getRelayQuote] userRango error: %v", err)
			}
			return respErr, nil
		}
	}

	if originChain.Name == constant.SOLANA_CHAIN_NAME_Relay {
		solanaHash, err = chainHelper.GetSolBlockHash()
		if err != nil {
			return nil, fmt.Errorf("failed to get Solana block hash: %v", err)
		}
	} else {
		nonceStr, err := chainHelper.GetEthUserNonce(originToken.ChainIdHex, req.User)
		if err != nil {
			return nil, fmt.Errorf("failed to get user nonce: %v", err)
		}
		_nonce, err := strconv.ParseInt(nonceStr, 0, 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse nonce: %v", err)
		}
		nonce = uint64(_nonce)
	}

	err = resp.Assign(quote, nonce, &solanaHash, originChain)
	if err != nil {
		return nil, err
	}
	resp.Type = req.Type
	//fmt.Println(time.Now().UnixMilli())
	return &resp, nil
}

func (s *Swap) CheckRelayStatus(req request.ReqCheckStatus) (*rango.RespCheckStatus, error) {
	resp1, err := relay.GetStatus(req.RequestId)
	if err != nil {
		return nil, fmt.Errorf("failed to get relay status: %v", err)
	}
	return &rango.RespCheckStatus{
		Status: resp1.Status,
	}, nil
}
