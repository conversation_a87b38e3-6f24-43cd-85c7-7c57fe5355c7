package swap

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/cache"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/constant"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/response"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"math/big"
	"regexp"
	"strings"
)

func IsExceedCapacity(quoteValueStr, capacityStr string, priceUse float64) (bool, string, error) {
	value := new(big.Int)
	capacity := new(big.Int)

	// 字符串转 big.Int
	if _, ok := value.SetString(quoteValueStr, 10); !ok {
		return false, "", errors.New("invalid quote value")
	}
	if _, ok := capacity.SetString(capacityStr, 10); !ok {
		return false, "", errors.New("invalid capacity value")
	}

	// 计算 capacity * 0.6
	sixty := big.NewRat(6, 10) // 0.6
	capacityRat := new(big.Rat).SetInt(capacity)
	thresholdRat := new(big.Rat).Mul(capacityRat, sixty)
	valueRat := new(big.Rat).SetInt(value)

	// 比较 value > thresholdInt
	if valueRat.Cmp(thresholdRat) > 0 {
		// 计算价格
		price := new(big.Rat).SetFloat64(priceUse)
		totalPrice := new(big.Rat).Mul(thresholdRat, price)
		return true, totalPrice.FloatString(6), nil
	}
	return false, "", nil
}

func RespQuoteErrorCode(err error, typeCode, description string) (*response.RespQuote, error) {
	respErrCode := &response.RespQuote{
		Type:        typeCode,
		Description: description,
	}
	if strings.Contains(err.Error(), constant.USE_RANGO_ERRCODE) {
		respErrCode.ErrorCode = constant.USE_RANGO_ERRCODE
		return respErrCode, nil
	} else if strings.Contains(err.Error(), "INSUFFICIENT_LIQUIDITY") {
		respErrCode.ErrorCode = constant.USE_RANGO_ERRCODE
		//eg:INSUFFICIENT_LIQUIDITY Amount is higher than the available liquidity. Max amount is $910,665 USD
		re := regexp.MustCompile(`\$(.*?)\s*USD`)
		match := re.FindStringSubmatch(err.Error())

		if len(match) > 1 {
			amount := match[1]
			respErrCode.Description = amount
		}
		return respErrCode, nil

	} else if strings.Contains(err.Error(), "Make sure you have at least") {
		respErrCode.ErrorCode = constant.BALANCE_NOT_ENOUGH_ERRCODE
		return respErrCode, nil
	} else if strings.Contains(err.Error(), "AMOUNT_TOO_LOW") {
		respErrCode.ErrorCode = constant.AMOUNT_TOO_LOW_ERRCODE
		return respErrCode, nil
	}
	return respErrCode, err
}

func storeApproveTx(rangoId string, result *response.RespQuote) error {
	if result == nil {
		return errors.New("result cannot be nil")
	}
	if !result.Items[0].IsApprovalTx { // only need cache approval tx
		return nil
	}
	bytes, err := json.Marshal(result)
	if err != nil {
		return fmt.Errorf("failed to marshal result: %v", err)
	}
	err = global.GVA_REDIS.Set(context.Background(), cache.KeyRangoCreateTx(rangoId), string(bytes),
		0).Err()
	if err != nil {
		return err
	}
	return nil
}

func loadApproveTx(rangoId string) (*response.RespQuote, error) {
	result, err := global.GVA_REDIS.Get(context.Background(), cache.KeyRangoCreateTx(rangoId)).Result()
	if err != nil {
		return nil, err
	}
	var resp response.RespQuote
	err = json.Unmarshal([]byte(result), &resp)
	if err != nil {
		return nil, err
	}
	return &resp, err
}
