package swap

import (
	"fmt"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	aggregate2 "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/aggregate"
	swap2 "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/swap"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/relay"
	"go.uber.org/zap"
)

func (r *swapTask) SyncRelayMeta() func() {
	return func() {
		if r.repo == nil {
			global.GVA_LOG.Error("[task] ResetRelayMetaData swap is nil")
			return
		}
		if err := r.ResetRelayMetaData(); err != nil {
			global.GVA_LOG.Error("[task] ResetRelayMetaData ", zap.Any("err", err))
		}
	}
}

func (r *swapTask) ResetRelayMetaData() error {
	chains, err := relay.GetMetaData()
	if err != nil {
		return fmt.Errorf("%v", err)
	}
	chainsModel, tokensModel, err := swap2.RelayChainsAssign(chains)
	if err != nil {
		return fmt.Errorf("failed to assign chains: %v", err)
	}
	swap := swap2.Swap
	err = swap.SaveRelayMetaData(chainsModel, tokensModel)
	if err != nil {
		return fmt.Errorf("failed to save relay metadata: %v", err)
	}
	aggregate := aggregate2.Aggregate
	_, err = aggregate.SwapMetaData()
	return nil
}
