package swap

import (
	"fmt"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	swap2 "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/swap"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/relay"
	"go.uber.org/zap"
	"strconv"
	"time"
)

func (r *swapTask) SyncRelayCapacity() func() {
	return func() {
		if r.repo == nil {
			global.GVA_LOG.Error("[task] SyncRelayCapacity swap repo is nil")
			return
		}
		if err := r.ResetRelayCapacity(); err != nil {
			global.GVA_LOG.Error("[task] SyncRelayCapacity ", zap.Any("err", err))
		}
	}
}

func (r *swapTask) ResetRelayCapacity() error {
	var (
		datas []*model.RelayCapacity
	)

	swap := swap2.Swap

	tokens, err := swap.GetRelayTokens()
	if err != nil {
		return err
	}
	for _, token := range tokens {
		chainIdInt, err := strconv.ParseInt(token.ChainId, 10, 64)
		if err != nil {
			return err
		}
		time.Sleep(time.Second)
		tokenId := token.TokenId
		if token.Address == "0x0000000000000000000000000000000000000000" || token.Address == "11111111111111111111111111111111" {
			tokenId = ""
		}
		resp, err := relay.GetConfig(0, chainIdInt, "", tokenId)
		if err != nil {
			continue
		}
		datas = append(datas, &model.RelayCapacity{
			ChainId:        token.ChainId,
			TokenId:        token.TokenId,
			CapacityVolume: resp.Solver.CapacityPerRequest,
			SolverAddress:  resp.Solver.Address,
		})

	}
	err = swap.SaveRelayCapacity(datas)
	if err != nil {
		return fmt.Errorf("failed to save relay metadata: %v", err)
	}
	all, err := swap.AllRelayCapacity()
	if err != nil {
		return err
	}
	for _, d := range all {
		_, err := swap.GetRelayCapacity(d.ChainId, d.TokenId)
		if err != nil {
			return fmt.Errorf("failed to get relay capacity: %v", err)
		}
		//fmt.Println(v)
	}
	return nil
}
