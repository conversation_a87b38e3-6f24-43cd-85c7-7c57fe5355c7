package verify

import (
	"bytes"
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"strings"
	"sync"
	"time"

	"net/http"

	"github.com/pierrec/lz4/v4"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo"
)

const (
	QueueBufferSize   = 100
	WorkerPoolSize    = 3
	InitialDelay      = 1 * time.Second
	MaxDelay          = 10 * time.Second
	MaxRetries        = 5
	FixedBuildAddress = "0x3fafd8c201ecfea482348553c263dd2e4e7407dd" // Write it first, then the address will change.
)

type CSVRecord struct {
	Time             string          `json:"time"`
	User             string          `json:"user"`
	Coin             string          `json:"coin"`
	Side             string          `json:"side"`
	Px               string          `json:"px"`
	Sz               string          `json:"sz"`
	Crossed          string          `json:"crossed"`
	SpecialTradeType string          `json:"special_trade_type"`
	Tif              string          `json:"tif"`
	IsTrigger        string          `json:"is_trigger"`
	Counterparty     string          `json:"counterparty"`
	ClosedPnl        string          `json:"closed_pnl"`
	TwapID           string          `json:"twap_id"`
	BuilderFee       decimal.Decimal `json:"builder_fee"`
	Cloid            string          `json:"cloid,omitempty"` // Possible new fields
}

type WorkItem struct {
	BuildAddress string `json:"buildAddress"`
	Date         string `json:"date"`
	Retries      int    `json:"retries"`
}

type VerificationResult struct {
	BuildAddress     string `json:"buildAddress"`
	Date             string `json:"date"`
	TotalRecords     int    `json:"totalRecords"`
	MatchedRecords   int    `json:"matchedRecords"`
	UnmatchedRecords int    `json:"unmatchedRecords"`
	Error            error  `json:"error,omitempty"`
}

type VerifyProcessor struct {
	verifyRepo     repo.VerifyRepo
	wg             sync.WaitGroup
	workerPoolSize int
	workQueue      chan WorkItem
}

func NewVerifyProcessor(repo repo.VerifyRepo) *VerifyProcessor {
	return &VerifyProcessor{
		verifyRepo:     repo,
		workerPoolSize: WorkerPoolSize,
		workQueue:      make(chan WorkItem, QueueBufferSize),
	}
}

func (p *VerifyProcessor) VerifyBuildFee(ctx context.Context) func() {
	return func() {
		global.GVA_LOG.Info("[task] Starting HyperLiquid BuildFee verification")

		p.startWorkerPool(ctx)

		if err := p.queueWorkItems(ctx, []string{FixedBuildAddress}); err != nil {
			global.GVA_LOG.Error("[task] Failed to queue work items", zap.Error(err))
		}

		p.wg.Wait()

		close(p.workQueue)

		global.GVA_LOG.Info("[task] HyperLiquid BuildFee verification completed")
	}
}

func (p *VerifyProcessor) startWorkerPool(ctx context.Context) {
	for i := 0; i < p.workerPoolSize; i++ {
		p.wg.Add(1)
		go p.worker(ctx, i)
	}
}

func (p *VerifyProcessor) worker(ctx context.Context, workerID int) {
	defer p.wg.Done()
	global.GVA_LOG.Info("[task] Worker started", zap.Int("worker_id", workerID))

	for {
		select {
		case <-ctx.Done():
			global.GVA_LOG.Info("[task] Worker stopped", zap.Int("worker_id", workerID))
			return
		case work, ok := <-p.workQueue:
			if !ok {
				global.GVA_LOG.Info("[task] Worker finished", zap.Int("worker_id", workerID))
				return
			}
			p.processWorkItem(ctx, work, workerID)
		}
	}
}

func (p *VerifyProcessor) buildCSVURL(dateStr, buildAddress string) string {
	return fmt.Sprintf("https://stats-data.hyperliquid.xyz/Mainnet/builder_fills/%s/%s.csv.lz4", buildAddress, dateStr)
}

func (p *VerifyProcessor) downloadParseCSV(csvURL string) ([][]string, error) {
	delay := InitialDelay
	var lastErr error

	for attempt := 0; attempt < MaxRetries; attempt++ {
		time.Sleep(delay)
		if attempt > 0 {
			delay = delay * 2
			if delay > MaxDelay {
				delay = MaxDelay
			}
		}

		client := &http.Client{Timeout: 60 * time.Second}
		resp, err := client.Get(csvURL)
		if err != nil {
			lastErr = fmt.Errorf("failed to download csv file: %w", err)
			continue // Network Error Retry
		}
		defer resp.Body.Close()

		if resp.StatusCode == http.StatusTooManyRequests { // 429
			lastErr = fmt.Errorf("rate limited (429), attempt %d", attempt+1)
			continue // Exponential backoff retry
		}
		if resp.StatusCode != http.StatusOK {
			return nil, fmt.Errorf("failed to download csv file, status: %d", resp.StatusCode)
		}

		compressedData, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("failed to read compressed data: %w", err)
		}

		decomperssData, err := p.decompressLZ4(compressedData)
		if err != nil {
			return nil, fmt.Errorf("failed to decompress data: %w", err)
		}

		reader := csv.NewReader(strings.NewReader(string(decomperssData)))
		records, err := reader.ReadAll()
		if err != nil {
			return nil, fmt.Errorf("failed to parse csv: %w", err)
		}

		return records, nil
	}

	return nil, fmt.Errorf("download csv failed after %d retries, last error: %v", MaxRetries, lastErr)
}

func (p *VerifyProcessor) decompressLZ4(data []byte) ([]byte, error) {
	zr := lz4.NewReader(bytes.NewReader(data))

	var buf bytes.Buffer
	_, err := io.Copy(&buf, zr)
	if err != nil {
		return nil, fmt.Errorf("failed to decompress data: %w", err)
	}

	return buf.Bytes(), nil
}

func (p *VerifyProcessor) processWorkItem(ctx context.Context, work WorkItem, workerID int) {
	global.GVA_LOG.Info("[task] Processing work item",
		zap.Int("worker_id", workerID),
		zap.String("date", work.Date))

	result := p.processCSVFile(ctx, work.BuildAddress, work.Date, workerID)
	if result.Error == nil && result.TotalRecords > 0 {
		global.GVA_LOG.Info("[task] Successfully processed CSV file",
			zap.Int("total_records", result.TotalRecords))
	} else if result.Error != nil {
		global.GVA_LOG.Warn("[task] Failed to process CSV file",
			zap.String("date", work.Date),
			zap.Error(result.Error))
	}
}

func (p *VerifyProcessor) processCSVFile(ctx context.Context, date, buildAddress string, workerID int) VerificationResult {
	result := VerificationResult{
		BuildAddress: buildAddress,
		Date:         date,
	}

	csvURL := p.buildCSVURL(buildAddress, date)

	records, err := p.downloadParseCSV(csvURL)
	if err != nil {
		result.Error = fmt.Errorf("failed to download/parse CSV: %w", err)
		return result
	}

	if len(records) <= 1 {
		result.Error = fmt.Errorf("CSV file is empty or contains only header")
		return result
	}

	result.TotalRecords = len(records) - 1

	dbOrders, err := p.verifyRepo.GetOrdersForCSVVerification(date)
	if err != nil {
		result.Error = fmt.Errorf("failed to get orders for verification: %w", err)
		return result
	}

	if len(dbOrders) == 0 {
		result.Error = fmt.Errorf("no orders found for verification on date %s", date)
		return result
	}

	matchedCount, unmatchedCount, err := p.verifyRecords(records, dbOrders, date, buildAddress, csvURL)
	if err != nil {
		result.Error = fmt.Errorf("failed to verify records: %w", err)
		return result
	}

	result.MatchedRecords = matchedCount
	result.UnmatchedRecords = unmatchedCount

	return result
}

func (p *VerifyProcessor) verifyRecords(csvRecords [][]string, dbOrders []*model.HyperLiquidVerifyOrder, csvDate string, buildAddress, csvURL string) (int, int, error) {
	matchedCount := 0
	unmatchedCount := 0

	dbOrderMap := make(map[string]*model.HyperLiquidVerifyOrder)
	for _, order := range dbOrders {
		dbOrderMap[order.Cloid] = order
	}

	for i, record := range csvRecords {
		if i == 0 {
			continue
		}

		if len(record) < 13 {
			global.GVA_LOG.Warn("[task] Invalid CSV record format",
				zap.Int("line", i+1),
				zap.Int("fields", len(record)))
			continue
		}

		csvRecord, err := p.parseCSVRecord(record)
		if err != nil {
			global.GVA_LOG.Warn("[task] Failed to parse CSV record",
				zap.Int("line", i+1),
				zap.Error(err))
			continue
		}

		if dbOrder, exists := dbOrderMap[csvRecord.Cloid]; exists {
			if p.validateMatch(csvRecord, dbOrder) {
				dbOrder.CSVVerified = true
				// Here you can add CSV validation fields
				if err := p.verifyRepo.UpdateOrder(dbOrder); err != nil {
					global.GVA_LOG.Error("[task] Failed to update order CSV verification status",
						zap.String("user", csvRecord.User),
						zap.String("order_id", dbOrder.ID.String()),
						zap.Error(err))
				} else {
					global.GVA_LOG.Info("[task] Successfully verified order with CSV",
						zap.String("user", csvRecord.User),
						zap.String("order_id", dbOrder.ID.String()),
					)
					matchedCount++
				}
			} else {
				unmatchedCount++
				global.GVA_LOG.Error("[task] CSV validation failed for order",
					zap.String("user", csvRecord.User),
					zap.String("order_id", dbOrder.ID.String()),
					zap.String("build_address", buildAddress),
					zap.String("csv_fee", csvRecord.BuilderFee.String()),
					zap.String("db_build_fee", dbOrder.BuildFee.String()))
			}
		} else {
			unmatchedCount++
			global.GVA_LOG.Warn("[task] CSV record not found in database",
				zap.String("user", csvRecord.User),
				zap.String("build_address", buildAddress),
				zap.String("coin", csvRecord.Coin),
				zap.String("side", csvRecord.Side))
		}
	}

	return matchedCount, unmatchedCount, nil
}

func (p *VerifyProcessor) parseCSVRecord(record []string) (*CSVRecord, error) {
	if len(record) < 13 {
		return nil, fmt.Errorf("insufficient fields in CSV record")
	}

	builderFee, err := decimal.NewFromString(record[12])
	if err != nil {
		return nil, fmt.Errorf("invalid builder fee: %w", err)
	}

	csvRecord := &CSVRecord{
		Time:             record[0],
		User:             record[1],
		Coin:             record[2],
		Side:             record[3],
		Px:               record[4],
		Sz:               record[5],
		Crossed:          record[6],
		SpecialTradeType: record[7],
		Tif:              record[8],
		IsTrigger:        record[9],
		Counterparty:     record[10],
		ClosedPnl:        record[11],
		BuilderFee:       builderFee,
	}

	if len(record) > 13 {
		csvRecord.Cloid = record[13]
	}

	return csvRecord, nil
}

func (p *VerifyProcessor) validateMatch(csvRecord *CSVRecord, dbOrder *model.HyperLiquidVerifyOrder) bool {
	if csvRecord.Coin != dbOrder.Base {
		global.GVA_LOG.Warn("[task] Coin mismatch in CSV verification",
			zap.String("csv_coin", csvRecord.Coin),
			zap.String("db_base", dbOrder.Base),
			zap.String("user", csvRecord.User))
		return false
	}

	if csvRecord.Side != dbOrder.Side {
		global.GVA_LOG.Warn("[task] Side mismatch in CSV verification",
			zap.String("csv_side", csvRecord.Side),
			zap.String("db_side", dbOrder.Side),
			zap.String("user", csvRecord.User))
		return false
	}

	csvSize, err := decimal.NewFromString(csvRecord.Sz)
	if err != nil {
		global.GVA_LOG.Warn("[task] Invalid size in CSV",
			zap.String("size", csvRecord.Sz),
			zap.String("user", csvRecord.User))
		return false
	}

	if !csvSize.Equal(dbOrder.Size) {
		global.GVA_LOG.Warn("[task] Size mismatch in CSV verification",
			zap.String("csv_size", csvSize.String()),
			zap.String("db_size", dbOrder.Size.String()),
			zap.String("user", csvRecord.User))
		return false
	}

	if !dbOrder.Price.IsZero() {
		csvPrice, err := decimal.NewFromString(csvRecord.Px)
		if err != nil {
			global.GVA_LOG.Warn("[task] Invalid price in CSV",
				zap.String("price", csvRecord.Px),
				zap.String("user", csvRecord.User))
			return false
		}

		if !csvPrice.Equal(dbOrder.Price) {
			global.GVA_LOG.Warn("[task] Price mismatch in CSV verification",
				zap.String("csv_price", csvPrice.String()),
				zap.String("db_price", dbOrder.Price.String()),
				zap.String("user", csvRecord.User))
			return false
		}
	}

	// Verify buildFee (main verification point)
	if !csvRecord.BuilderFee.Equal(dbOrder.BuildFee) {
		global.GVA_LOG.Error("[task] BuildFee mismatch in CSV verification",
			zap.String("csv_build_fee", csvRecord.BuilderFee.String()),
			zap.String("db_build_fee", dbOrder.BuildFee.String()),
			zap.String("user", csvRecord.User))
		return false
	}

	return true
}

func (p *VerifyProcessor) queueWorkItems(ctx context.Context, buildAddresses []string) error {
	today := time.Now().Format("20060102")
	for _, address := range buildAddresses {
		select {
		case p.workQueue <- WorkItem{
			BuildAddress: address,
			Date:         today,
			Retries:      0,
		}:
		case <-ctx.Done():
			return ctx.Err()
		}
	}

	global.GVA_LOG.Info("[task] Queued work items",
		zap.Int("count", len(buildAddresses)))

	return nil
}
