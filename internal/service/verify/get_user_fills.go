package verify

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
)

// createHTTPClient creates an HTTP client with proxy support if available
func createHTTPClient(timeout time.Duration) *http.Client {
	// Try to get proxy manager from global variable
	if global.GVA_PROXY_MANAGER != nil {
		// Use reflection to avoid import cycle
		if pm, ok := global.GVA_PROXY_MANAGER.(interface {
			CreateHTTPClient(time.Duration) *http.Client
		}); ok {
			return pm.CreateHTTPClient(timeout)
		}
	}

	// Fallback to regular HTTP client
	return &http.Client{Timeout: timeout}
}

type UserFillsRequest struct {
	Type string `json:"type"`
	User string `json:"user"`
}

type Fill struct {
	ClosedPnl     string `json:"closedPnl"`
	Coin          string `json:"coin"`
	Crossed       bool   `json:"crossed"`
	Direction     string `json:"dir"`
	Fee           string `json:"fee"`
	Hash          string `json:"hash"`
	Oid           int64  `json:"oid"`
	Price         string `json:"px"`
	Side          string `json:"side"`
	StartPosition string `json:"startPosition"`
	Size          string `json:"sz"`
	Time          int64  `json:"time"`
}

func GetUserFills(userAddress string) ([]Fill, error) {
	// Create the request payload
	requestData := UserFillsRequest{
		Type: "userFills",
		User: userAddress,
	}

	// Marshal the request data to JSON
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("error marshaling JSON: %w", err)
	}

	// Create the HTTP request
	req, err := http.NewRequest("POST", "https://api.hyperliquid.xyz/info", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("error creating request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("User-Agent", "go-http-client/1.1")

	// Send the request
	client := createHTTPClient(10 * time.Second)
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error sending request: %w", err)
	}
	defer resp.Body.Close()

	// Read the response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response: %w", err)
	}

	// Check if the response was successful
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("request failed with status: %s, body: %s", resp.Status, string(body))
	}

	// Parse the response
	var fills []Fill
	err = json.Unmarshal(body, &fills)
	if err != nil {
		return nil, fmt.Errorf("error parsing response: %w", err)
	}

	return fills, nil
}
