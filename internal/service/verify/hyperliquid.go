package verify

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/cache"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/request"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/response"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	natsClient "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/nats"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/verify"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
)

type HyperLiquidVerifyService struct {
	verifyRepo *verify.VerifyOrderRepository
	NatsClient *natsClient.NATSClient
}

func NewHyperLiquidVerifyService(client *natsClient.NATSClient) *HyperLiquidVerifyService {
	return &HyperLiquidVerifyService{
		verifyRepo: &verify.VerifyOrderRepository{},
		NatsClient: client,
	}
}

func (s *HyperLiquidVerifyService) GenerateCloid(ctx context.Context, count int) (*response.RespGenerateCloid, error) {
	userId := GetUserIDFromContext(ctx)
	var cloids []string
	var orders []*model.HyperLiquidVerifyOrder

	for i := 0; i < count; i++ {
		input := fmt.Sprintf("%s:%d:%d", cache.KeyVerifyOrderCloid(), time.Now().UnixNano(), i)
		hash := sha256.Sum256([]byte(input))
		cloid := "0x" + hex.EncodeToString(hash[:16])
		cloids = append(cloids, cloid)

		order := &model.HyperLiquidVerifyOrder{
			ID:         uuid.New(),
			Cloid:      cloid,
			UserID:     userId,
			SourceData: input,
			CreatedAt:  time.Now(),
			UpdatedAt:  time.Now(),
		}
		orders = append(orders, order)
	}

	go func() {
		if err := s.verifyRepo.CreateOrders(orders); err != nil {
			global.GVA_LOG.Error("Failed to create batch orders", zap.Error(err))
			return
		}
		global.GVA_LOG.Info("Successfully created batch orders",
			zap.Int("count", len(orders)),
			zap.String("user_id", userId.String()))
	}()

	return &response.RespGenerateCloid{
		Cloids: cloids,
		Count:  len(cloids),
	}, nil
}
func (s *HyperLiquidVerifyService) Transaction(ctx context.Context, req []request.ReqTransaction) (*response.RespTransaction, error) {
	err := s.buildOrderModel(ctx, req)
	if err != nil {
		return nil, err
	}

	// affiliateSent := s.sendAffiliateEventIfNeeded(req)
	// global.GVA_LOG.Info("Verify order completed",
	// 	zap.Bool("affiliate_send", affiliateSent))

	return &response.RespTransaction{
		Status: "success",
	}, nil
}

func (s *HyperLiquidVerifyService) buildOrderModel(ctx context.Context, req []request.ReqTransaction) error {
	for _, transaction := range req {
		if transaction.OrderStatus == "cancel" || transaction.Operation == "cancelOrder" {
			order, err := s.parseInputByTokenForCancel(ctx, transaction)
			if err != nil {
				return fmt.Errorf("failed build order model for cancel with cancelOrder: %w", err)
			}
			if err := s.verifyRepo.UpdateTransactionByToken(order); err != nil {
				global.GVA_LOG.Info("Failed to update order by token for cancel",
					zap.String("cloid", transaction.Cloid),
					zap.String("err :", err.Error()),
				)
				return err
			}
			continue
		}

		order, err := s.parseInputByCloid(transaction)
		if err != nil {
			return fmt.Errorf("failed build order model: %w", err)
		}

		if transaction.OrderStatus == "filled" && transaction.OrderStatus != "cancel" {
			if err := s.handleFilledOrder(ctx, order, transaction); err != nil {
				global.GVA_LOG.Error("Failed to handle filled order",
					zap.String("cloid", transaction.Cloid),
					zap.Error(err))
				return err
			}
		}

		if err := s.verifyRepo.UpdateTransactionByCloid(order); err != nil {
			global.GVA_LOG.Info("Failed to create order by cloid",
				zap.String("cloid", transaction.Cloid),
				zap.String("err :", err.Error()),
			)
			return err
		}
	}

	return nil
}

func (s *HyperLiquidVerifyService) handleFilledOrder(ctx context.Context, order *model.HyperLiquidVerifyOrder, transaction request.ReqTransaction) error {
	// buildFee: price * size * 0.01%
	if !order.Price.IsZero() && !order.Size.IsZero() {
		feeRate := decimal.NewFromFloat(0.0001) // 0.01% = 0.0001
		order.BuildFee = order.Price.Mul(order.Size).Mul(feeRate)
		global.GVA_LOG.Info("Calculated build fee for filled order",
			zap.String("cloid", transaction.Cloid),
			zap.String("build_fee", order.BuildFee.String()))
	}

	if transaction.WalletAddress != "" {
		fills, err := GetUserFills(transaction.WalletAddress)
		if err != nil {
			global.GVA_LOG.Error("Failed to get user fills",
				zap.String("wallet_address", transaction.WalletAddress),
				zap.Error(err))
			return err
		}

		var matchedFill *Fill
		for _, fill := range fills {
			if fill.Oid == order.OID || fill.Hash == order.Hash {
				matchedFill = &fill
				break
			}
		}

		if matchedFill != nil {
			if s.validateFillMatch(order, matchedFill) {
				global.GVA_LOG.Info("Fill validation successful",
					zap.String("cloid", transaction.Cloid),
					zap.String("fill_oid", fmt.Sprintf("%d", matchedFill.Oid)))

				if err := s.sendFillVerifiedEvent(order, matchedFill); err != nil {
					global.GVA_LOG.Error("Failed to send fill verified event",
						zap.String("cloid", transaction.Cloid),
						zap.Error(err))
					return err
				}

				order.IsVerified = true
			} else {
				global.GVA_LOG.Warn("Fill validation failed",
					zap.String("cloid", transaction.Cloid),
					zap.String("fill_oid", fmt.Sprintf("%d", matchedFill.Oid)))
			}
		} else {
			global.GVA_LOG.Warn("No matching fill found for order",
				zap.String("cloid", transaction.Cloid),
				zap.String("wallet_address", transaction.WalletAddress))
		}
	}

	return nil
}

func (s *HyperLiquidVerifyService) validateFillMatch(order *model.HyperLiquidVerifyOrder, fill *Fill) bool {
	if order.Coin != fill.Coin {
		global.GVA_LOG.Warn("Coin mismatch in fill validation",
			zap.String("order_coin", order.Coin),
			zap.String("fill_coin", fill.Coin))
		return false
	}

	fillSize, err := decimal.NewFromString(fill.Size)
	if err != nil {
		global.GVA_LOG.Error("Invalid fill size",
			zap.String("fill_size", fill.Size),
			zap.Error(err))
		return false
	}

	if !fillSize.Equal(order.Size) {
		global.GVA_LOG.Warn("Size mismatch in fill validation",
			zap.String("order_size", order.Size.String()),
			zap.String("fill_size", fillSize.String()))
		return false
	}

	fillPrice, err := decimal.NewFromString(fill.Price)
	if err != nil {
		global.GVA_LOG.Error("Invalid fill price",
			zap.String("fill_price", fill.Price),
			zap.Error(err))
		return false
	}

	if !fillPrice.Equal(order.Price) {
		global.GVA_LOG.Warn("Price mismatch in fill validation",
			zap.String("order_price", order.Price.String()),
			zap.String("fill_price", fillPrice.String()))
		return false
	}

	return true
}

func (s *HyperLiquidVerifyService) sendFillVerifiedEvent(order *model.HyperLiquidVerifyOrder, fill *Fill) error {
	if s.NatsClient == nil {
		return fmt.Errorf("service dependency 'NatsClient' is not initialized")
	}

	event := map[string]interface{}{
		"type":         "fill_verified",
		"cloid":        order.Cloid,
		"user_address": order.UserAddress,
		"coin":         order.Coin,
		"side":         order.Side,
		"size":         order.Size.String(),
		"price":        order.Price.String(),
		"build_fee":    order.BuildFee.String(),
		"fill_oid":     fill.Oid,
		"fill_hash":    fill.Hash,
		"fill_time":    fill.Time,
		"verified_at":  time.Now().Unix(),
	}

	eventData, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal fill verified event: %w", err)
	}

	// NATS JetStream
	_, err = s.NatsClient.PublishJS(utils.HyperliquidVerifySubject, eventData)
	if err != nil {
		return fmt.Errorf("failed to publish fill verified event: %w", err)
	}

	global.GVA_LOG.Info("Fill verified event sent",
		zap.String("cloid", order.Cloid),
		zap.Int64("fill_oid", fill.Oid))
	return nil
}

func (s *HyperLiquidVerifyService) parseInputByCloid(req request.ReqTransaction) (*model.HyperLiquidVerifyOrder, error) {
	order, err := s.verifyRepo.GetOrderByCloid(req.Cloid)
	if err != nil {
		return nil, fmt.Errorf("failed to get order by cloid: %w", err)
	}
	if order == nil {
		return nil, fmt.Errorf("order with cloid '%s' not found", req.Cloid)
	}
	if req.BaseCoin != "" {
		order.Coin = req.BaseCoin
	}
	if req.AvgPx != "" {
		avgPx, err := decimal.NewFromString(req.AvgPx)
		if err != nil {
			return nil, fmt.Errorf("parse request avgPx err:%w ", err)
		}
		order.AvgPrice = avgPx
	}
	if req.OrderType != "" {
		order.OrderType = req.OrderType
	}
	if req.Grouping != "" {
		order.Grouping = req.Grouping
	}
	if req.Operation != "" {
		order.Operation = req.Operation
	}
	if req.Price != "" {
		price, err := decimal.NewFromString(req.Price)
		if err != nil {
			return nil, fmt.Errorf("parse request price err:%w ", err)
		}
		order.Price = price
	}
	if req.Side != "" {
		order.Side = req.Side
	}
	if req.Size != "" {
		size, err := decimal.NewFromString(req.Size)
		if err != nil {
			return nil, fmt.Errorf("parse request size err:%w ", err)
		}
		order.Size = size
	}
	if req.WalletAddress != "" {
		order.UserAddress = req.WalletAddress
	}
	if req.TotalSz != "" {
		order.TotalSz = req.TotalSz
	}
	if req.OrderStatus != "" {
		order.Status = req.OrderStatus
	}
	if req.Created != "" {
		millis, err := strconv.ParseInt(req.Created, 10, 64)
		if err != nil {
			return nil, err
		}
		orderCreateTime := time.UnixMilli(millis)
		order.OrderCreateAt = orderCreateTime
	}
	if req.Cloid != "" {
		order.Cloid = req.Cloid
	}
	order.UpdatedAt = time.Now()
	if req.Oid != "" {
		oid, err := strconv.ParseInt(req.Oid, 10, 64)
		if err != nil {
			fmt.Println("conversion error:", err)
			return nil, err
		}
		order.OID = oid
	}
	if req.AvgPx != "" && req.TotalSz != "" {
		feeRate := decimal.NewFromFloat(0.0001) // 0.01% = 0.0001
		order.BuildFee = order.AvgPrice.Mul(order.TotalFee).Mul(feeRate)
	}

	return order, nil
}

func (s *HyperLiquidVerifyService) parseInputByTokenForCancel(ctx context.Context, req request.ReqTransaction) (*model.HyperLiquidVerifyOrder, error) {
	userID := GetUserIDFromContext(ctx)
	order, err := s.verifyRepo.GetOrderByTokenAndCriteria(req, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get order by token and criteria: %w", err)
	}
	if order == nil {
		return nil, fmt.Errorf("order not found for token criteria")
	}

	if req.BaseCoin != "" {
		order.Coin = req.BaseCoin
	}
	if req.AvgPx != "" {
		avgPx, err := decimal.NewFromString(req.AvgPx)
		if err != nil {
			return nil, fmt.Errorf("parse request avgPx err:%w ", err)
		}
		order.AvgPrice = avgPx
	}
	if req.OrderType != "" {
		order.OrderType = req.OrderType
	}
	if req.Grouping != "" {
		order.Grouping = req.Grouping
	}
	if req.Operation != "" {
		order.Operation = req.Operation
	}
	if req.Price != "" {
		price, err := decimal.NewFromString(req.Price)
		if err != nil {
			return nil, fmt.Errorf("parse request price err:%w ", err)
		}
		order.Price = price
	}
	if req.Side != "" {
		order.Side = req.Side
	}
	if req.Size != "" {
		size, err := decimal.NewFromString(req.Size)
		if err != nil {
			return nil, fmt.Errorf("parse request size err:%w ", err)
		}
		order.Size = size
	}
	if req.WalletAddress != "" {
		order.UserAddress = req.WalletAddress
	}
	if req.TotalSz != "" {
		order.TotalSz = req.TotalSz
	}
	if req.OrderStatus != "" {
		order.Status = req.OrderStatus
	}
	if req.Created != "" {
		millis, err := strconv.ParseInt(req.Created, 10, 64)
		if err != nil {
			return nil, err
		}
		orderCreateTime := time.UnixMilli(millis)
		order.OrderCreateAt = orderCreateTime
	}
	if req.Cloid != "" {
		order.Cloid = req.Cloid
	}
	order.UpdatedAt = time.Now()
	if req.Oid != "" {
		oid, err := strconv.ParseInt(req.Oid, 10, 64)
		if err != nil {
			fmt.Println("conversion error by cancel:", err)
			return nil, err
		}
		order.OID = int64(oid)
	}
	return order, nil
}

// func (s *HyperLiquidVerifyService) sendAffiliateEventIfNeeded(req []request.ReqTransaction) bool {
// 	var event []request.ReqContractTxEvent
// 	for _, tran := range req {
// 		size, err := decimal.NewFromString(tran.Size)
// 		if err != nil {
// 			return false
// 		}
// 		price, err := decimal.NewFromString(tran.Price)
// 		if err != nil {
// 			return false
// 		}
// 		avgPx, err := decimal.NewFromString(tran.AvgPx)
// 		if err != nil {
// 			return false
// 		}
// 		oid := 0
// 		if tran.Oid != "" {
// 			oid, err := strconv.ParseInt(tran.Oid, 10, 64)
// 			if err != nil {
// 				fmt.Println("conversion error by event:", err)
// 				return false
// 			}
// 			oid = int64(oid)
// 		}
// 		affiliateEvent := &request.ReqContractTxEvent{
// 			Cloid:       tran.Cloid,
// 			UserAddress: tran.WalletAddress,
// 			Status:      tran.OrderStatus,
// 			OID:         int64(oid),
// 			TotalSz:     tran.TotalSz,
// 			OrderType:   tran.OrderType,
// 			Side:        tran.Side,
// 			Size:        size,
// 			Price:       price,
// 			AvgPrice:    avgPx,
// 			Grouping:    tran.Grouping,
// 			Operation:   tran.Operation,
// 			Coin:        tran.BaseCoin,
// 			CreatedAt:   tran.Created,
// 		}
// 		event = append(event, *affiliateEvent)
// 	}

// 	if err := s.sendContractTxEvent(event); err != nil {
// 		global.GVA_LOG.Error("Failed to send contract event", zap.Error(err))
// 		return false
// 	}

// 	return true
// }

// func (s *HyperLiquidVerifyService) sendContractTxEvent(event []request.ReqContractTxEvent) error {
// 	if s.NatsClient == nil {
// 		return fmt.Errorf("service dependency 'NatsClient' is not initialized")
// 	}
// 	eventData, err := json.Marshal(event)
// 	if err != nil {
// 		return fmt.Errorf("failed to marshal ContractTx event: %w", err)
// 	}

// 	// send NATS JetStream
// 	_, err = s.NatsClient.PublishJS(utils.HyperliquidVerifySubject, eventData)
// 	if err != nil {
// 		return fmt.Errorf("failed to publish contract tx event: %w", err)
// 	}

// 	global.GVA_LOG.Info("ContractTx event sent")
// 	return nil
// }

func (s *HyperLiquidVerifyService) GetOrderByCloid(cloid string) (*model.HyperLiquidVerifyOrder, error) {
	return s.verifyRepo.GetOrderByCloid(cloid)
}

func (s *HyperLiquidVerifyService) GetUnverifiedOrders() ([]*model.HyperLiquidVerifyOrder, error) {
	return s.verifyRepo.GetUnverifiedOrder()
}

func (s *HyperLiquidVerifyService) GetOrdersForCSVVerification(csvData string) ([]*model.HyperLiquidVerifyOrder, error) {
	return s.verifyRepo.GetOrdersForCSVVerification(csvData)
}

func (s *HyperLiquidVerifyService) UpdateOrder(order *model.HyperLiquidVerifyOrder) error {
	return s.verifyRepo.UpdateOrder(order)
}

func GetUserIDFromContext(ctx context.Context) uuid.UUID {
	userIdStr, _ := ctx.Value("userId").(string)
	userId := uuid.Nil
	if userIdStr != "" {
		userId, _ = uuid.Parse(userIdStr)
	}
	return userId
}
