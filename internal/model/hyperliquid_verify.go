package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

type HyperLiquidVerifyOrder struct {
	ID            uuid.UUID       `gorm:"primary_key;type:uuid;column:id"`
	CreatedAt     time.Time       `gorm:"column:created_at"`
	UpdatedAt     time.Time       `gorm:"column:updated_at"`
	OrderCreateAt time.Time       `gorm:"column:order_created_at"`
	Cloid         string          `gorm:"index;column:cloid;uniqueIndex"`
	UserID        uuid.UUID       `gorm:"index;column:user_id;type:uuid"`
	UserAddress   string          `gorm:"user_address;index"`
	Side          string          `gorm:"column:side"`       // long/short or ask/bid
	OrderType     string          `gorm:"column:order_type"` // Market/Limit/Trigger
	Symbol        string          `gorm:"column:symbol"`     // "BTC-USDT-PERP"
	IsBuy         bool            `gorm:"column:is_buy"`     // Redundant fields for easy logical judgment
	Leverage      int             `gorm:"column:leverage"`   // Leverage multiple
	Margin        decimal.Decimal `gorm:"column:margin"`     // Margin amount
	IsMarket      bool            `gorm:"column:is_market"`  // is market order
	TriggerPx     string          `gorm:"column:trigger_px"` // trigger price
	Tpsl          string          `gorm:"column:tpsl"`       // tp or sl
	Tif           string          `gorm:"column:tif"`        // Time In Force: Alo/Ioc/Gtc
	Base          string          `gorm:"column:base"`       // "BTC"（base token）
	Quote         string          `gorm:"column:quote"`      // "USDC"（Denominated token）
	Size          decimal.Decimal `gorm:"column:size;type:decimal(20,8)"`
	Price         decimal.Decimal `gorm:"column:price;type:decimal(20,8)"`
	AvgPrice      decimal.Decimal `gorm:"column:avg_price;type:decimal(20,8)"`
	BuildFee      decimal.Decimal `gorm:"column:build_fee;type:decimal(20,8)"`
	TotalFee      decimal.Decimal `gorm:"column:total_fee;type:decimal(20,8)"`
	FeeBp         int             `gorm:"column:fee_bp"`
	BuildAddress  string          `gorm:"column:build_address"`
	Status        string          `gorm:"index;column:status"`
	OID           int64           `gorm:"column:oid"`
	TotalSz       string          `gorm:"column:total_sz"`

	Hash       string `gorm:"column:hash"`
	Coin       string `gorm:"column:coin"`
	Asset      string `gorm:"column:asset"`
	ReduceOnly bool   `gorm:"column:reduce_only"`
	Grouping   string `gorm:"grouping"`
	Operation  string `gorm:"operation"`

	SourceData  string          `gorm:"column:source_data"`
	IsVerified  bool            `gorm:"column:is_verified;index;default:false"`
	CSVVerified bool            `gorm:"column:csv_verified;index;default:false"`
	ExpectedFee decimal.Decimal `gorm:"column:expected_fee;type:decimal(10,6)"`
	ActualFee   decimal.Decimal `gorm:"column:actual_fee;type:decimal(10,6)"`
	FeeValid    bool            `gorm:"column:fee_valid;default:false"`

	AffiliateEventSent bool `gorm:"column:affiliate_event_sent;default:false"`
}

func (HyperLiquidVerifyOrder) TableName() string {
	return "hyperliquid_verify_order"
}
