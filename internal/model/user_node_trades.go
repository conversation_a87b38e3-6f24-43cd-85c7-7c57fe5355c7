package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

type UserNodeTrades struct {
	ID               uuid.UUID       `gorm:"primary_key;type:uuid;column:id"`
	Side             string          `gorm:"column:side;type:varchar(16)"`
	Time             time.Time       `gorm:"column:time"`
	Coin             string          `gorm:"column:coin;type:varchar(16)"`
	Hash             string          `gorm:"column:hash;type:varchar(66)"`
	Size             decimal.Decimal `gorm:"column:size;type:decimal(20,8)"`  // sz
	Price            decimal.Decimal `gorm:"column:price;type:decimal(20,8)"` // px
	TradeDirOverride string          `gorm:"column:trade_dir_override;type:varchar(16)"`
}

func (UserNodeTrades) TableName() string {
	return "user_node_trades"
}

type UserNodeTradesSideInfo struct {
	TradeID  uuid.UUID `gorm:"index;column:trade_id;type:uuid"`
	User     string    `gorm:"column:user;type:varchar(64)"`
	StartPos string    `gorm:"column:start_pos"`
	OID      int64     `gorm:"column:oid"`
	TwapID   *int64    `gorm:"column:twap_id"`
	Cloid    *string   `gorm:"index;column:cloid"`
}

func (UserNodeTradesSideInfo) TableName() string {
	return "user_node_trades_side_info"
}
