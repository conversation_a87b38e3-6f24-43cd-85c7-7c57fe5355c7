package app

import (
	"fmt"
	"log"
	"net/http"
	"time"

	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/nats"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/hyperliquid"

	"github.com/99designs/gqlgen/graphql/handler"
	"github.com/99designs/gqlgen/graphql/playground"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	nats_lib "github.com/nats-io/nats.go"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/controller/graphql"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/controller/graphql/gql_error"
	middlewares "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/controller/graphql/middlewares"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/initializer"
)

type GraphqlServer struct {
	Router     *gin.Engine
	HttpServer *http.Server
}

func (server *GraphqlServer) Initialize() {
	global.GVA_VP = initializer.Viper()
	global.GVA_LOG = initializer.Zap()
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initializer.Gorm()
	global.GVA_CLICKHOUSE = initializer.Clickhouse()
	initializer.DBList()
	global.GVA_NATS_MEME = nats.InitNatsJetStream(global.GVA_CONFIG.NatsMeme)
	global.GVA_NATS_DEX = nats.InitNatsJetStream(global.GVA_CONFIG.NatsDex)

	if global.GVA_CONFIG.System.UseMultipoint || global.GVA_CONFIG.System.UseRedis {
		// init redis server
		initializer.Redis()
		initializer.RedisList()
	}

	if global.GVA_NATS_DEX != nil {
		_, err := global.GVA_NATS_DEX.AddStream(
			&nats_lib.StreamConfig{
				Name:     utils.HyperliquidTransactionStream,
				Subjects: []string{utils.HyperliquidTransactionEvent},
				Storage:  nats_lib.FileStorage,
				MaxAge:   72 * time.Hour,
			},
		)

		if err != nil {
			global.GVA_LOG.Error("Failed to add stream hyperliquid transaction", zap.Error(err))
		} else {
			global.GVA_LOG.Info("Added stream for hyperliquid transaction", zap.String("stream", utils.HyperliquidTransactionStream))
		}
	}

	if global.GVA_CONFIG.Hyperliquid.UseMainNet {
		global.GVA_HYPERLIQUID_URL = hyperliquid.MainnetURL
		global.GVA_HYPERLIQUID_INFO_ENDPOINT = hyperliquid.InfoAPIMainnetEndpoint
	} else {
		global.GVA_HYPERLIQUID_URL = hyperliquid.TestnetURL
		global.GVA_HYPERLIQUID_INFO_ENDPOINT = hyperliquid.InfoAPITestnetEndpoint
	}

	// Initialize proxy manager
	initializer.InitProxyManager()

	if global.GVA_CONFIG.System.UseMongo {
		err := initializer.Mongo.Initialization()
		if err != nil {
			zap.L().Error(fmt.Sprintf("%+v", err))
		}
	}

	port := global.GVA_CONFIG.System.Addr

	router := gin.New()
	router.Use(gin.Recovery())
	router.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"},
		AllowHeaders:     []string{"*"},
		ExposeHeaders:    []string{"*"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))
	// router.Use(middlewares.ErrorHandler())
	// router.Use(middlewares.RequestLogger())
	router.Use(middlewares.GinGqlContext()) // this will attach gin.Context into graphql context

	// health check route
	pingHandler := func(c *gin.Context) {
		c.JSON(200, gin.H{
			"pong": time.Now().UnixMilli(),
		})
	}

	api := router.Group(global.GVA_CONFIG.System.GraphqlPrefix)
	api.GET("/ping", pingHandler)
	api.GET("/healthz", pingHandler)

	initGraphqlServer(router)

	server.Router = router
	log.Printf("GraphQL playground is starting on http://localhost:%d/", port)

	server.HttpServer = &http.Server{
		Addr:         fmt.Sprintf(":%d", port),
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		// MaxHeaderBytes: 1 << 20,
		Handler: server.Router,
	}

	global.GVA_LOG.Info(fmt.Sprintf("Listening to port :%d", port))
}

func initGraphqlServer(router *gin.Engine) {
	api := router.Group(global.GVA_CONFIG.System.GraphqlPrefix)
	// graphql has only this router
	playGroundHandler := playground.Handler("Crypto-Future: GraphQL playground", fmt.Sprintf("%s", global.GVA_CONFIG.System.GraphqlPrefix))
	graphqlServer := handler.NewDefaultServer(
		graphql.NewExecutableSchema(graphql.Config{
			Resolvers: graphql.NewRootResolver(),
		}),
	)
	graphqlServer.SetErrorPresenter(gql_error.CustomErrorPresenter)

	// With Basic Auth middleware
	// this only apply basic auth for playground, not schema, so jwt is also needed for all request
	// authorized := router.Group(global.GVA_CONFIG.System.GraphqlPrefix, gin.BasicAuth(gin.Accounts{
	// 	// configs.Env.GqlPlaygroundUser: configs.Env.GqlPlaygroundPass,
	// }))

	api.GET("/playground", func(c *gin.Context) {
		playGroundHandler.ServeHTTP(c.Writer, c.Request)
	})

	api.POST("", middlewares.GqlJwtAuth(), func(c *gin.Context) {
		graphqlServer.ServeHTTP(c.Writer, c.Request)
	})

}

func (server *GraphqlServer) Run() {
	server.HttpServer.ListenAndServe()
}
