package gql_error

import (
	"context"
	"errors"

	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils/app_error"

	"github.com/99designs/gqlgen/graphql"
	"github.com/vektah/gqlparser/v2/gqlerror"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"go.uber.org/zap"
)

func CustomErrorPresenter(ctx context.Context, err error) *gqlerror.Error {
	gqlErr := graphql.DefaultErrorPresenter(ctx, err)
	var appGqlErr *utils.APIError
	if errors.As(err, &appGqlErr) {
		gqlErr.Message = appGqlErr.Message
		gqlErr.Extensions = map[string]interface{}{
			"code": appGqlErr.ErrorCode,
			"meta": appGqlErr.Message,
		}
		global.GVA_LOG.Error(
			"extensions.code", zap.String("code", appGqlErr.ErrorCode),
		)
		global.GVA_LOG.Error(
			"error", zap.String("error", gqlErr.Error()),
		)
	} else {
		gqlErr.Extensions = map[string]interface{}{
			"code": app_error.Unknown,
		}
		global.GVA_LOG.Error("error", zap.String("error", gqlErr.Error()))
	}

	return gqlErr
}
