# NOTE: this graphql playground is for use & partner access
# so please don't include admin operation here, use /playground-admin instead
directive @auth on FIELD_DEFINITION

type Query {
  # Swap
  getExchangeMetaV2: ExchangeMetaResponse!
  getHistoriesV2(input: HistoryRequest!): HistoryResponse!
  checkStatusV2(input: CheckStatusRequest!): CheckStatusResponse!
  getQuoteV2(input: QuoteRequest!): QuoteResponse!

  getExchangeMeta: RangoMetaResponse!
  getHistories(input: RangoHistoryRequest!): RangoHistoryResponse!
  checkStatus(input: CheckStatusRequest!): CheckStatusResponse!
  checkApproval(input: CheckApprovalRequest!): CheckApprovalResponse!
  getBestRoute(input: GetBestRouteRequest!): GetBestRouteResponse!
  getAllPossibleRoutes(input: GetAllPossibleRoutesRequest!): GetAllPossibleRoutesResponse!


  # Symbol
  getOHLC(input: OHLCRequest!): OHLCResponse!
  getSymbolList(input: SymbolListRequest!): SymbolListResponse!
  getSymbolDetail(input: SymbolDetailRequest!): SymbolDetailResponse!
  getFavoriteSymbols: FavoriteSymbolsResponse! @auth
  getUserSymbolPreference(input: UserSymbolPreferenceRequest!): UserSymbolPreferenceResponse! @auth
  getCategory: CategoryResponse!
  getPopularSymbol(input: PopularSymbolRequest!): PopularSymbolResponse!
  searchSymbol(input: SearchSymbolRequest!): SearchSymbolResponse!
  getNewSymbol: NewSymbolResponse!
  getAlertSymbolsSetting: GetAlertSymbolSettingResponse! @auth
  getSignals(input: SignalRequest!): SignalResponse!

  # Trading
  getUserOpenOrder(input: UserOpenOrderRequest!): UserOpenOrderResponse! @auth
  getUserPosition(input: UserPositionRequest!): UserPositionResponse! @auth
  getUserTradeHistory(input: UserTradeHistoryRequest!): UserTradeHistoryResponse! @auth
  getUserPrevDayBalance(input: UserPrevDayBalanceRequest!): UserPrevDayBalanceResponse! @auth

  # Order
  getUserOrders(input: UserOrderStatusRequest!): UserOrderStatusResponse! @auth
  # Event
  getUserMiscEvents(input: UserMiscEventRequest!): UserMiscEventResponse! @auth
  # Trade
  getUserNodeTrades(input: UserNodeTradesRequest!): RespGetUserNodeTrades! @auth
  # Asset
  callbackUserBalance(input: CallbackUserBalanceRequest!): CallbackUserBalanceResponse! @auth
  # Verify
  generateCloid(input: GenerateCloidInput): GenerateCloidResponse! @auth
}

type Mutation {
  # Swap
  callBack(input: CallBackRequest!): CallBackResponse!
  createTx(input: CreateTxRequest!): CreateTxResponse! @auth
  confirmRoute(input: ConfirmRouteRequest!): ConfirmRouteResponse!
  signTx(input: CreateTxRequest!): SignTxResponse! @auth

  # Symbol
  upsertFavoriteSymbol(input: UpsertFavoriteSymbolRequest!): UpsertFavoriteSymbolResponse! @auth
  updateUserSymbolPreference(input: UpdateUserSymbolPreferenceRequest!): UserSymbolPreferenceResponse! @auth
  createAlertSymbolSetting(input: CreateAlertSymbolSettingRequest!): CreateAlertSymbolSettingResponse! @auth
  updateAlertSymbolSetting(input: UpdateAlertSymbolSettingRequest!): UpdateAlertSymbolSettingResponse! @auth
  deleteAlertSymbolSetting(input: DeleteAlertSymbolSettingRequest!): DeleteAlertSymbolSettingResponse! @auth

  # Trading
  storeTxInformation(input: StoreTxInformationRequest!): StoreTxInformationResponse! @auth

  # Verify
  logTransaction(input: [LogTransactionInput]!): LogTransactionResponse! @auth
}
