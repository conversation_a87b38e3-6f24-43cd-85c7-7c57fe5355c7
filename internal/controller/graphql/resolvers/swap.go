package resolvers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"

	"github.com/go-playground/validator/v10"
	"github.com/samber/lo"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/controller/graphql/gql_model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/request"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/response"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/aggregate"
	repoSwap "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/swap"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/external"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/swap"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
	user_signing "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/protogen/user/v1"
)

type SwapResolver struct {
	s           service.SwapI
	v           *validator.Validate
	userService user_signing.UserSigningServiceClient
}

func NewSwapResolver() *SwapResolver {
	swapService := swap.NewSwapService(repoSwap.Swap, aggregate.Aggregate)
	userService := external.NewUserGrpcClient()

	return &SwapResolver{
		s:           swapService,
		v:           validator.New(validator.WithRequiredStructEnabled()),
		userService: *userService,
	}
}

func (r *SwapResolver) GetExchangeMeta() (*gql_model.RangoMetaResponse, error) {
	chains, tokens, swappers, err := r.s.GetRangoMeta()
	if err != nil {
		return nil, err
	}

	var (
		respExchangeMeta response.RespExchangeMeta
	)

	err = respExchangeMeta.TokenAssign(tokens, chains, swappers)
	if err != nil {
		return nil, err
	}

	return utils.Translate[gql_model.RangoMetaResponse](respExchangeMeta), nil
}

func (r *SwapResolver) GetBestRoute(input gql_model.GetBestRouteRequest) (*gql_model.GetBestRouteResponse, error) {
	req := request.ReqGetBestRoute{
		FromBlockChain:   input.FromBlockChain,
		FromSymbol:       input.FromSymbol,
		FromAddress:      input.FromAddress,
		FromTokenAddress: utils.StringPointerToString(input.FromTokenAddress),
		ToBlockChain:     input.ToBlockChain,
		ToSymbol:         input.ToSymbol,
		ToTokenAddress:   utils.StringPointerToString(input.ToTokenAddress),
		ToAddress:        input.ToAddress,
		Amount:           input.Amount,
	}

	if err := req.ValidateBasic(); err != nil {
		return nil, fmt.Errorf("invalid request parameters: %v", err)
	}

	resp, err := r.s.GetBestRoute(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get best route: %v", err)
	}

	return utils.Translate[gql_model.GetBestRouteResponse](resp), nil
}

func (r *SwapResolver) GetAllPossibleRoutes(input gql_model.GetAllPossibleRoutesRequest) (*gql_model.GetAllPossibleRoutesResponse, error) {
	req := request.ReqGetAllPossibleRoutes{
		FromBlockchain:   input.FromBlockchain,
		FromSymbol:       input.FromSymbol,
		FromTokenAddress: utils.StringPointerToString(input.FromTokenAddress),
		ToBlockchain:     input.ToBlockchain,
		ToSymbol:         input.ToSymbol,
		ToTokenAddress:   utils.StringPointerToString(input.ToTokenAddress),
		Amount:           input.Amount,
		Slippage:         input.Slippage,
	}
	if err := req.ValidateBasic(); err != nil {
		return nil, fmt.Errorf("invalid request parameters: %v", err)
	}

	resp, err := r.s.GetAllPossibleRoutes(req)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch routes: %v", err)
	}

	return utils.Translate[gql_model.GetAllPossibleRoutesResponse](resp), nil
}

func (r *SwapResolver) ConfirmRoute(input gql_model.ConfirmRouteRequest) (*gql_model.ConfirmRouteResponse, error) {
	req := request.ReqConfirmRoute{
		RequestId:   input.RequestID,
		Destination: input.Destination,
		SelectedWallets: lo.Map(input.SelectedWallets, func(w *gql_model.SelectedWallet, _ int) request.SelectedWallet {
			return request.SelectedWallet{
				Blockchain: w.Blockchain,
				Address:    w.Address,
			}
		}),
	}

	if err := req.ValidateBasic(); err != nil {
		return nil, fmt.Errorf("invalid request parameters: %w", err)
	}

	resp, err := r.s.ConfirmRoute(req)
	if err != nil {
		return nil, fmt.Errorf("failed to confirm route: %w", err)
	}

	var responseErr *string
	if resp.Error != "" {
		responseErr = &resp.Error
	}

	response := &gql_model.ConfirmRouteResponse{
		RequestID: resp.RequestId,
		Status:    resp.Status,
		RouteErr:  responseErr,
	}

	return response, nil
}

func (r *SwapResolver) CheckApproval(input gql_model.CheckApprovalRequest) (*gql_model.CheckApprovalResponse, error) {
	req := request.ReqCheckApproval{
		RequestId: input.RequestID,
		TxHash:    input.TxHash,
	}

	if err := req.ValidateBasic(); err != nil {
		return nil, fmt.Errorf("invalid request parameters: %w", err)
	}

	// Call service
	resp, err := r.s.CheckApproval(req)
	if err != nil {
		return nil, fmt.Errorf("failed to check approval: %w", err)
	}

	return utils.Translate[gql_model.CheckApprovalResponse](resp), nil
}

func (r *SwapResolver) CreateTx(input gql_model.CreateTxRequest, ctx context.Context) (*gql_model.CreateTxResponse, error) {
	req := request.ReqCreateTx{
		RequestId: input.RequestID,
		Step:      int64(input.Step),
		UserSettings: request.UserSettings{
			Slippage:        input.UserSettings.Slippage,
			InfiniteApprove: input.UserSettings.InfiniteApprove,
		},
		Validations: request.Validations{
			Balance: input.Validations.Balance,
			Fee:     input.Validations.Fee,
			Approve: input.Validations.Approve,
		},
	}

	// verify
	if err := utils.Verify(req, utils.CreateTxVerify); err != nil {
		return nil, fmt.Errorf("validation error: %w", err)
	}

	// call service
	createTXResponse, err := r.s.CreateOrUpdateTx(req)
	if err != nil {
		return nil, fmt.Errorf("failed to create/update tx: %w", err)
	}
	var (
		chainType = user_signing.ChainType_EVM
		url       = ""
		nonce     = createTXResponse.Transaction.Nonce
	)

	switch createTXResponse.Transaction.BlockChain {
	case "ETH":
		url = "https://practical-sly-flower.quiknode.pro/15d8ffa9b7c9ed209296efe6de4c43b5be3c26ea/"
		chainType = user_signing.ChainType_EVM
	case "BSC":
		url = "https://restless-clean-forest.bsc.quiknode.pro/64781ec6fb0190c54cb91cac33aeb01f2cab50e7"
		chainType = user_signing.ChainType_BSC
	case "POLYGON":
		chainType = user_signing.ChainType_POLYGON
	case "ARBITRUM":
		url = "https://summer-distinguished-breeze.arbitrum-mainnet.quiknode.pro/6681c4b31fa02ee860081babc885d918c856a012/"
		chainType = user_signing.ChainType_ARB
	case "SOLANA":
		chainType = user_signing.ChainType_SOLANA
	}
	if url != "" && nonce == "" {
		nonce, err = r.ethGetTransactionCount(url, createTXResponse.Transaction.From)
		if err != nil {
			return nil, fmt.Errorf("failed to get nonce: %w %v", err, createTXResponse.Transaction)
		}
	}
	signedTx, err := r.userService.SignEvmTransaction(
		context.Background(),
		&user_signing.SignUserTransactionEvmRequest{
			Chain:                chainType,
			UserId:               ctx.Value("userId").(string),
			To:                   createTXResponse.Transaction.To,
			Data:                 createTXResponse.Transaction.Data,
			Value:                &createTXResponse.Transaction.Value,
			MaxFeePerGas:         &createTXResponse.Transaction.MaxFeePerGas,
			MaxPriorityFeePerGas: &createTXResponse.Transaction.MaxPriorityFeePerGas,
			GasLimit:             &createTXResponse.Transaction.GasLimit,
			GasPrice:             &createTXResponse.Transaction.GasPrice,
			From:                 createTXResponse.Transaction.From,
			Nonce:                nonce,
		},
	)

	if err != nil {
		return nil, fmt.Errorf("failed to sign transaction: %w %v", err, createTXResponse.Transaction)
	}

	gqlResponse := utils.Translate[gql_model.CreateTxResponse](createTXResponse)
	gqlResponse.SignUserTransactionEvm = &gql_model.SignUserTransactionEvmResponse{
		UserID:               signedTx.UserId,
		To:                   signedTx.To,
		Data:                 signedTx.Data,
		Value:                signedTx.Value,
		MaxFeePerGas:         signedTx.MaxFeePerGas,
		MaxPriorityFeePerGas: signedTx.MaxPriorityFeePerGas,
		GasLimit:             signedTx.GasLimit,
		GasPrice:             signedTx.GasPrice,
		From:                 signedTx.From,
		//Chain:                gql_model.ChainType(signedTx.Chain),
		SignedTransaction: signedTx.SignedTransaction,
	}

	return gqlResponse, nil
}

func (r *SwapResolver) SignUserTx(input gql_model.CreateTxRequest, ctx context.Context) (*gql_model.SignTxResponse, error) {
	req := request.ReqCreateTx{
		RequestId: input.RequestID,
		Step:      int64(input.Step),
		UserSettings: request.UserSettings{
			Slippage:        input.UserSettings.Slippage,
			InfiniteApprove: input.UserSettings.InfiniteApprove,
		},
		Validations: request.Validations{
			Balance: input.Validations.Balance,
			Fee:     input.Validations.Fee,
			Approve: input.Validations.Approve,
		},
	}

	// verify
	if err := utils.Verify(req, utils.CreateTxVerify); err != nil {
		return nil, fmt.Errorf("validation error: %w", err)
	}

	// call service
	result, err := r.s.CreatedTx(req)
	if err != nil {
		return nil, fmt.Errorf("failed to create/update tx: %w", err)
	}

	chainType := user_signing.ChainType_EVM

	switch result.Transaction.BlockChain {
	case "Ethereum":
		chainType = user_signing.ChainType_EVM
	case "BSC":
		chainType = user_signing.ChainType_BSC
	case "Polygon":
		chainType = user_signing.ChainType_POLYGON
	case "Arbitrum":
		chainType = user_signing.ChainType_ARB
	case "Solana":
		chainType = user_signing.ChainType_SOLANA
	}

	signedTx, err := r.userService.SignEvmTransaction(
		context.Background(),
		&user_signing.SignUserTransactionEvmRequest{
			Chain:                chainType,
			UserId:               ctx.Value("userId").(string),
			To:                   result.Transaction.To,
			Data:                 result.Transaction.Data,
			Value:                &result.Transaction.Value,
			MaxFeePerGas:         &result.Transaction.MaxFeePerGas,
			MaxPriorityFeePerGas: &result.Transaction.MaxPriorityFeePerGas,
			GasLimit:             &result.Transaction.GasLimit,
			GasPrice:             &result.Transaction.GasPrice,
			From:                 result.Transaction.From,
			Nonce:                result.Transaction.Nonce,
		},
	)

	if err != nil {
		return nil, fmt.Errorf("failed to sign transaction: %w", err)
	}

	return &gql_model.SignTxResponse{
		BlockChain:           result.Transaction.BlockChain,
		To:                   result.Transaction.To,
		Data:                 result.Transaction.Data,
		Value:                result.Transaction.Value,
		MaxFeePerGas:         result.Transaction.MaxFeePerGas,
		MaxPriorityFeePerGas: result.Transaction.MaxPriorityFeePerGas,
		GasLimit:             result.Transaction.GasLimit,
		GasPrice:             result.Transaction.GasPrice,
		From:                 result.Transaction.From,
		SignedTransaction:    signedTx.SignedTransaction,
	}, nil
}

func (r *SwapResolver) CallBack(input gql_model.CallBackRequest) (*gql_model.CallBackResponse, error) {
	req := request.ReqCallBack{
		RequestId: input.RequestID,
		Step:      int64(input.Step),
		TxHash:    input.TxHash,
	}

	resp, err := r.s.SendCallBack(req)
	if err != nil {
		return nil, fmt.Errorf("failed to call back: %w", err)
	}

	result := utils.Translate[gql_model.CallBackResponse](resp)
	return result, nil
}

func (r *SwapResolver) CheckStatus(input gql_model.CheckStatusRequest) (*gql_model.CheckStatusResponse, error) {
	if err := utils.Verify(input, utils.TxStatusVerify); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	resp, err := r.s.CheckSwapStatus(request.ReqCheckStatus{
		RequestId: input.RequestID,
		TxHash:    input.TxHash,
		Step:      int64(input.Step),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to check status: %w", err)
	}

	result := utils.TranslateByJSON[gql_model.CheckStatusResponse](resp)
	return result, nil
}

func (r *SwapResolver) GetHistories(input gql_model.RangoHistoryRequest) (*gql_model.RangoHistoryResponse, error) {
	// You might expand logic to handle pagination/time filtering here
	list, total, err := r.s.Histories(input.Blockchain, input.Address)
	if err != nil {
		return nil, fmt.Errorf("failed to get histories: %w", err)
	}

	var resp response.RespHistory
	err = resp.HistoryAssign(total, list)
	if err != nil {
		return nil, err
	}

	return utils.Translate[gql_model.RangoHistoryResponse](resp), nil
}

func (r *SwapResolver) ethGetTransactionCount(url, address string) (string, error) {
	payload := map[string]interface{}{
		"jsonrpc": "2.0",
		"id":      1,
		"method":  "eth_getTransactionCount",
		"params":  []interface{}{address, "latest"},
	}
	body, _ := json.Marshal(payload)
	resp, err := http.Post(url, "application/json", bytes.NewBuffer(body))
	if err != nil {
		fmt.Println("Request error:", err)
		return "", err
	}
	defer resp.Body.Close()
	respBody, _ := ioutil.ReadAll(resp.Body)

	var response struct {
		Id      int    `json:"id"`
		Jsonrpc string `json:"jsonrpc"`
		Result  string `json:"result"`
		Error   struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
		} `json:"error"`
	}
	err = json.Unmarshal(respBody, &response)
	if err != nil {
		fmt.Println("Error unmarshalling response:", err)
		return "", err
	}
	return response.Result, err
}
