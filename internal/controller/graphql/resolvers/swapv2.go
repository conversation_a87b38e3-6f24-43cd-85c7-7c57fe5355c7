package resolvers

import (
	"errors"
	"fmt"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/constant"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/controller/graphql/gql_model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/request"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/response"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
)

func (r *SwapResolver) GetExchangeMetaV2() (*gql_model.ExchangeMetaResponse, error) {
	var (
		respExchangeMeta response.RespExchangeMetaV2
	)
	metaData, err := r.s.GetMetaData()
	if err != nil {
		return nil, err
	}
	err = respExchangeMeta.Assign(metaData)
	if err != nil {
		return nil, err
	}
	return utils.Translate[gql_model.ExchangeMetaResponse](respExchangeMeta), nil
}

func (r *SwapResolver) GetQuoteV2(input gql_model.QuoteRequest) (*gql_model.QuoteResponse, error) {
	req := request.ReqGetQuote{
		User:          input.User,
		OriginId:      input.OriginID,
		DestinationId: input.DestinationID,
		Recipient:     input.Recipient,
		Amount:        input.Amount,
		Type:          input.Type,
	}
	var (
		resp *response.RespQuote
		err  error
	)
	if err = req.ValidateBasic(); err != nil {
		return nil, fmt.Errorf("invalid request parameters: %v", err)
	}
	if req.Type == constant.Relay { //approve 以后再去请求relay，就会返回一个正式的data
		resp, err = r.s.GetRelayQuote(req)
		if err != nil {
			return nil, fmt.Errorf("[GetRelayQuote]failed to get best route: %v", err)
		}
		if resp.ErrorCode == constant.USE_RANGO_ERRCODE {
			relayResp := *resp
			resp, err = r.s.RangoQuote(req)
			if err != nil {
				return nil, fmt.Errorf("[RangoQuote]failed to get best route: %v", err)
			}
			if resp.ErrorCode == "" {
				resp.ErrorCode = relayResp.ErrorCode
				resp.Description = relayResp.Description
			}
		}
	}
	if req.Type == constant.Rango { //用户approve以后再次去请求rango
		req.RequestId = *input.RequestID
		resp, err = r.s.RangoQuote(req)
		if err != nil {
			return nil, fmt.Errorf("[RangoQuote] failed to get best route: %v", err)
		}
	}

	return utils.Translate[gql_model.QuoteResponse](resp), nil
}

// todo cache RequestID check txhash
func (r *SwapResolver) CheckStatusV2(input gql_model.CheckStatusRequest) (*gql_model.CheckStatusResponse, error) {
	if err := utils.Verify(input, utils.TxStatusVerify); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}
	if isHexHash(input.RequestID) {
		resp, err := r.s.CheckRelayStatus(request.ReqCheckStatus{
			RequestId: input.RequestID,
			TxHash:    input.TxHash,
			Step:      int64(input.Step),
		})
		if err != nil {
			return nil, fmt.Errorf("failed to check relay status: %w", err)
		}
		//failure, fallback, pending, received, success
		if resp.Status == "unknown" || resp.Status == "pending" {
			resp.Status = "running"
		} else if resp.Status == "failure" || resp.Status == "fallback" {
			resp.Status = "failed"
		} else {
			resp.Status = "success"
		}
		result := utils.TranslateByJSON[gql_model.CheckStatusResponse](resp)
		return result, nil
	}
	resp, err := r.s.CheckSwapStatusV2(request.ReqCheckStatus{
		RequestId: input.RequestID,
		TxHash:    input.TxHash,
		Step:      int64(input.Step),
	})
	if err != nil {
		return nil, err
	}
	if resp.Error != "" {
		return nil, errors.New(resp.Error)
	}
	if resp.Status != "unknown" {
		resp.Status = "failed"
	}
	if resp.Status == "pending" {
		resp.Status = "running"
	}
	result := utils.TranslateByJSON[gql_model.CheckStatusResponse](resp)
	return result, nil
}

func isHexHash(id string) bool {
	return len(id) == 66 && len(id) > 2 && id[:2] == "0x"
}

func (r *SwapResolver) GetHistoriesV2(input gql_model.HistoryRequest) (*gql_model.HistoryResponse, error) {
	// You might expand logic to handle pagination/time filtering here
	list, total, err := r.s.Histories(input.Blockchain, input.Address)
	if err != nil {
		return nil, fmt.Errorf("failed to get histories: %w", err)
	}

	var resp response.RespHistory
	err = resp.HistoryAssign(total, list)
	if err != nil {
		return nil, err
	}

	return utils.Translate[gql_model.HistoryResponse](resp), nil
}
