package resolvers

import (
	"context"
	"fmt"

	"github.com/go-playground/validator/v10"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/controller/graphql/gql_model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/request"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/verify"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
)

type VerifyResolver struct {
	s service.VerifyI
	v *validator.Validate
}

func NewVerifyResolver() *VerifyResolver {
	return &VerifyResolver{
		s: verify.NewHyperLiquidVerifyService(global.GVA_NATS_DEX),
		v: validator.New(),
	}
}

// generate CLOID
func (r *VerifyResolver) GenerateCloid(ctx context.Context, input *gql_model.GenerateCloidInput) (*gql_model.GenerateCloidResponse, error) {
	count := 1
	if input != nil && input.Count != nil {
		count = *input.Count
	}
	resp, err := r.s.GenerateCloid(ctx, count)
	if err != nil {
		return nil, fmt.Errorf("failed to generate cloid: %w", err)
	}

	return utils.Translate[gql_model.GenerateCloidResponse](resp), nil
}

func (r *VerifyResolver) Transaction(ctx context.Context, input []*gql_model.LogTransactionInput) (*gql_model.LogTransactionResponse, error) {
	req, err := r.parseTransactionInput(input)
	if err != nil {
		return nil, fmt.Errorf("failed to parse input: %w", err)
	}

	resp, err := r.s.Transaction(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to verify order: %w", err)
	}

	return utils.Translate[gql_model.LogTransactionResponse](resp), nil
}

func (r *VerifyResolver) parseTransactionInput(input []*gql_model.LogTransactionInput) ([]request.ReqTransaction, error) {
	var transactions []request.ReqTransaction
	for _, transaction := range input {
		cloid := ""
		if transaction.Cloid != nil {
			cloid = *transaction.Cloid
		}
		baseCoin := ""
		if transaction.BaseCoin != nil {
			baseCoin = *transaction.BaseCoin
		}
		side := ""
		if transaction.Side != nil {
			side = GQLSideTypeToString(*transaction.Side)
		}
		price := ""
		if transaction.Price != nil {
			price = *transaction.Price
		}
		size := ""
		if transaction.Size != nil {
			size = *transaction.Size
		}
		orderType := ""
		if transaction.OrderType != nil {
			orderType = GQLOrderTypeToString(*transaction.OrderType)
		}
		operation := ""
		if transaction.Operation != nil {
			operation = GQLOperationTypeToString(*transaction.Operation)
		}
		orderStatus := ""
		if transaction.OrderStatus != nil {
			orderStatus = GQLOrderStatusToString(*transaction.OrderStatus)
		}
		avgPx := ""
		if transaction.AvgPx != nil {
			avgPx = *transaction.AvgPx
		}
		walletAddress := ""
		if transaction.WalletAddress != nil {
			walletAddress = *transaction.WalletAddress
		}
		totalSz := ""
		if transaction.TotalSz != nil {
			totalSz = *transaction.TotalSz
		}
		grouping := ""
		if transaction.Grouping != nil {
			grouping = GQLGroupingTypeToString(*transaction.Grouping)
		}
		created := ""
		if transaction.Created != nil {
			created = *transaction.Created
		}
		oid := ""
		if transaction.Oid != nil {
			oid = *transaction.Oid
		}

		req := request.ReqTransaction{
			Oid:           oid,
			BaseCoin:      baseCoin,
			Side:          side,
			Price:         price,
			Size:          size,
			OrderType:     orderType,
			Operation:     operation,
			OrderStatus:   orderStatus,
			Cloid:         cloid,
			AvgPx:         avgPx,
			TotalSz:       totalSz,
			Grouping:      grouping,
			Created:       created,
			WalletAddress: walletAddress,
		}
		transactions = append(transactions, req)
	}

	return transactions, nil
}

func GQLOperationTypeToString(operation gql_model.Operation) string {
	switch operation {
	case gql_model.OperationOpenOrder:
		return "open order"
	case gql_model.OperationCancelOrder:
		return "cancel order"
	default:
		return ""
	}
}

func GQLOrderTypeToString(orderType gql_model.OrderType) string {
	switch orderType {
	case gql_model.OrderTypeLimit:
		return "limit"
	case gql_model.OrderTypeMarket:
		return "market"
	case gql_model.OrderTypeTpMarket:
		return "tp_market"
	case gql_model.OrderTypeSlMarket:
		return "sl_market"
	default:
		return ""
	}
}

func GQLOrderStatusToString(orderStatus gql_model.OrderStatus) string {
	switch orderStatus {
	case gql_model.OrderStatusFilled:
		return "filled"
	case gql_model.OrderStatusResting:
		return "resting"
	case gql_model.OrderStatusCancel:
		return "cancel"
	default:
		return ""
	}
}

func GQLSideTypeToString(side gql_model.Side) string {
	switch side {
	case gql_model.SideBuy:
		return "buy"
	case gql_model.SideSell:
		return "sell"
	default:
		return ""
	}
}

func GQLGroupingTypeToString(grouping gql_model.Grouping) string {
	switch grouping {
	case gql_model.GroupingNa:
		return "na"
	case gql_model.GroupingNormalTpsl:
		return "normalTpsl"
	case gql_model.GroupingPositionTpsl:
		return "positionTpsl"
	default:
		return ""
	}
}
