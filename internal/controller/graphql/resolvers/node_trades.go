package resolvers

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/controller/graphql/gql_model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/request"
	repoNodeTrades "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/nodeTrades"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service"
	nodetrades "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/nodeTrades"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
)

type NodeTradesResolver struct {
	o service.NodeTradesI
}

func NewNodeTradesResolver() *NodeTradesResolver {
	nodeTradesService := nodetrades.NewNodeTradesService(repoNodeTrades.NodeTrades)

	return &NodeTradesResolver{
		o: nodeTradesService,
	}
}

func (r *NodeTradesResolver) GetUserNodeTrades(ctx context.Context, input gql_model.UserNodeTradesRequest) (*gql_model.RespGetUserNodeTrades, error) {
	userID, err := uuid.Parse(ctx.Value("userId").(string))
	if err != nil {
		return nil, fmt.Errorf("failed to parse user id: %w", err)
	}

	resp, err := r.o.GetNodeTrades(request.ReqGetUserNodeTrades{
		UserID: userID,
		LastID: uuid.MustParse(input.LastID),
		Limit:  input.Limit,
	})
	if err != nil {
		return nil, fmt.Errorf("get open trades failed: %w", err)
	}

	return utils.Translate[gql_model.RespGetUserNodeTrades](resp), nil
}
