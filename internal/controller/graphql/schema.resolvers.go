package graphql

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.70

import (
	"context"

	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/controller/graphql/gql_model"
)

// CallBack is the resolver for the callBack field.
func (r *mutationResolver) CallBack(ctx context.Context, input gql_model.CallBackRequest) (*gql_model.CallBackResponse, error) {
	return r.SwapResolver.CallBack(input)
}

// CreateTx is the resolver for the createTx field.
func (r *mutationResolver) CreateTx(ctx context.Context, input gql_model.CreateTxRequest) (*gql_model.CreateTxResponse, error) {
	return r.SwapResolver.CreateTx(input, ctx)
}

// ConfirmRoute is the resolver for the confirmRoute field.
func (r *mutationResolver) ConfirmRoute(ctx context.Context, input gql_model.ConfirmRouteRequest) (*gql_model.ConfirmRouteResponse, error) {
	return r.SwapResolver.ConfirmRoute(input)
}

// SignTx is the resolver for the signTx field.
func (r *mutationResolver) SignTx(ctx context.Context, input gql_model.CreateTxRequest) (*gql_model.SignTxResponse, error) {
	return r.SwapResolver.SignUserTx(input, ctx)
}

// UpsertFavoriteSymbol is the resolver for the upsertFavoriteSymbol field.
func (r *mutationResolver) UpsertFavoriteSymbol(ctx context.Context, input gql_model.UpsertFavoriteSymbolRequest) (*gql_model.UpsertFavoriteSymbolResponse, error) {
	return r.SymbolResolver.UpsertFavoriteSymbol(ctx, input)
}

// UpdateUserSymbolPreference is the resolver for the updateUserSymbolPreference field.
func (r *mutationResolver) UpdateUserSymbolPreference(ctx context.Context, input gql_model.UpdateUserSymbolPreferenceRequest) (*gql_model.UserSymbolPreferenceResponse, error) {
	return r.SymbolResolver.UpdateUserSymbolPreference(ctx, input)
}

// CreateAlertSymbolSetting is the resolver for the createAlertSymbolSetting field.
func (r *mutationResolver) CreateAlertSymbolSetting(ctx context.Context, input gql_model.CreateAlertSymbolSettingRequest) (*gql_model.CreateAlertSymbolSettingResponse, error) {
	return r.SymbolResolver.CreateAlertSymbolSetting(ctx, input)
}

// UpdateAlertSymbolSetting is the resolver for the updateAlertSymbolSetting field.
func (r *mutationResolver) UpdateAlertSymbolSetting(ctx context.Context, input gql_model.UpdateAlertSymbolSettingRequest) (*gql_model.UpdateAlertSymbolSettingResponse, error) {
	return r.SymbolResolver.UpdateAlertSymbolSetting(ctx, input)
}

// DeleteAlertSymbolSetting is the resolver for the deleteAlertSymbolSetting field.
func (r *mutationResolver) DeleteAlertSymbolSetting(ctx context.Context, input gql_model.DeleteAlertSymbolSettingRequest) (*gql_model.DeleteAlertSymbolSettingResponse, error) {
	return r.SymbolResolver.DeleteAlertSymbolSetting(ctx, input)
}

// StoreTxInformation is the resolver for the storeTxInformation field.
func (r *mutationResolver) StoreTxInformation(ctx context.Context, input gql_model.StoreTxInformationRequest) (*gql_model.StoreTxInformationResponse, error) {
	return r.TradingResolver.StoreTxInformation(ctx, input)
}

// LogTransaction is the resolver for the logTransaction field.
func (r *mutationResolver) LogTransaction(ctx context.Context, input []*gql_model.LogTransactionInput) (*gql_model.LogTransactionResponse, error) {
	return r.VerifyResolver.Transaction(ctx, input)
}

// GetExchangeMetaV2 is the resolver for the getExchangeMetaV2 field.
func (r *queryResolver) GetExchangeMetaV2(ctx context.Context) (*gql_model.ExchangeMetaResponse, error) {
	return r.SwapResolver.GetExchangeMetaV2()
}

// GetHistoriesV2 is the resolver for the getHistoriesV2 field.
func (r *queryResolver) GetHistoriesV2(ctx context.Context, input gql_model.HistoryRequest) (*gql_model.HistoryResponse, error) {
	return r.SwapResolver.GetHistoriesV2(input)
}

// CheckStatusV2 is the resolver for the checkStatusV2 field.
func (r *queryResolver) CheckStatusV2(ctx context.Context, input gql_model.CheckStatusRequest) (*gql_model.CheckStatusResponse, error) {
	return r.SwapResolver.CheckStatusV2(input)
}

// GetQuoteV2 is the resolver for the getQuoteV2 field.
func (r *queryResolver) GetQuoteV2(ctx context.Context, input gql_model.QuoteRequest) (*gql_model.QuoteResponse, error) {
	return r.SwapResolver.GetQuoteV2(input)
}

// GetExchangeMeta is the resolver for the getExchangeMeta field.
func (r *queryResolver) GetExchangeMeta(ctx context.Context) (*gql_model.RangoMetaResponse, error) {
	return r.SwapResolver.GetExchangeMeta()
}

// GetHistories is the resolver for the getHistories field.
func (r *queryResolver) GetHistories(ctx context.Context, input gql_model.RangoHistoryRequest) (*gql_model.RangoHistoryResponse, error) {
	return r.SwapResolver.GetHistories(input)
}

// CheckStatus is the resolver for the checkStatus field.
func (r *queryResolver) CheckStatus(ctx context.Context, input gql_model.CheckStatusRequest) (*gql_model.CheckStatusResponse, error) {
	return r.SwapResolver.CheckStatus(input)
}

// CheckApproval is the resolver for the checkApproval field.
func (r *queryResolver) CheckApproval(ctx context.Context, input gql_model.CheckApprovalRequest) (*gql_model.CheckApprovalResponse, error) {
	return r.SwapResolver.CheckApproval(input)
}

// GetBestRoute is the resolver for the getBestRoute field.
func (r *queryResolver) GetBestRoute(ctx context.Context, input gql_model.GetBestRouteRequest) (*gql_model.GetBestRouteResponse, error) {
	return r.SwapResolver.GetBestRoute(input)
}

// GetAllPossibleRoutes is the resolver for the getAllPossibleRoutes field.
func (r *queryResolver) GetAllPossibleRoutes(ctx context.Context, input gql_model.GetAllPossibleRoutesRequest) (*gql_model.GetAllPossibleRoutesResponse, error) {
	return r.SwapResolver.GetAllPossibleRoutes(input)
}

// GetOhlc is the resolver for the getOHLC field.
func (r *queryResolver) GetOhlc(ctx context.Context, input gql_model.OHLCRequest) (*gql_model.OHLCResponse, error) {
	return r.SymbolResolver.GetOHLC(input)
}

// GetSymbolList is the resolver for the getSymbolList field.
func (r *queryResolver) GetSymbolList(ctx context.Context, input gql_model.SymbolListRequest) (*gql_model.SymbolListResponse, error) {
	return r.SymbolResolver.GetSymbolList(input)
}

// GetSymbolDetail is the resolver for the getSymbolDetail field.
func (r *queryResolver) GetSymbolDetail(ctx context.Context, input gql_model.SymbolDetailRequest) (*gql_model.SymbolDetailResponse, error) {
	return r.SymbolResolver.GetSymbolDetail(input)
}

// GetFavoriteSymbols is the resolver for the getFavoriteSymbols field.
func (r *queryResolver) GetFavoriteSymbols(ctx context.Context) (*gql_model.FavoriteSymbolsResponse, error) {
	return r.SymbolResolver.GetFavoriteSymbols(ctx)
}

// GetUserSymbolPreference is the resolver for the getUserSymbolPreference field.
func (r *queryResolver) GetUserSymbolPreference(ctx context.Context, input gql_model.UserSymbolPreferenceRequest) (*gql_model.UserSymbolPreferenceResponse, error) {
	return r.SymbolResolver.GetUserSymbolPreference(ctx, input)
}

// GetCategory is the resolver for the getCategory field.
func (r *queryResolver) GetCategory(ctx context.Context) (*gql_model.CategoryResponse, error) {
	return r.SymbolResolver.GetCategory()
}

// GetPopularSymbol is the resolver for the getPopularSymbol field.
func (r *queryResolver) GetPopularSymbol(ctx context.Context, input gql_model.PopularSymbolRequest) (*gql_model.PopularSymbolResponse, error) {
	return r.SymbolResolver.GetPopularSymbol(input)
}

// SearchSymbol is the resolver for the searchSymbol field.
func (r *queryResolver) SearchSymbol(ctx context.Context, input gql_model.SearchSymbolRequest) (*gql_model.SearchSymbolResponse, error) {
	return r.SymbolResolver.SearchSymbol(input)
}

// GetNewSymbol is the resolver for the getNewSymbol field.
func (r *queryResolver) GetNewSymbol(ctx context.Context) (*gql_model.NewSymbolResponse, error) {
	return r.SymbolResolver.GetNewSymbol()
}

// GetAlertSymbolsSetting is the resolver for the getAlertSymbolsSetting field.
func (r *queryResolver) GetAlertSymbolsSetting(ctx context.Context) (*gql_model.GetAlertSymbolSettingResponse, error) {
	return r.SymbolResolver.GetAlertSymbolsSetting(ctx)
}

// GetSignals is the resolver for the getSignals field.
func (r *queryResolver) GetSignals(ctx context.Context, input gql_model.SignalRequest) (*gql_model.SignalResponse, error) {
	return r.SymbolResolver.GetSignals(ctx, input)
}

// GetUserOpenOrder is the resolver for the getUserOpenOrder field.
func (r *queryResolver) GetUserOpenOrder(ctx context.Context, input gql_model.UserOpenOrderRequest) (*gql_model.UserOpenOrderResponse, error) {
	return r.TradingResolver.GetUserOpenOrder(ctx, input)
}

// GetUserPosition is the resolver for the getUserPosition field.
func (r *queryResolver) GetUserPosition(ctx context.Context, input gql_model.UserPositionRequest) (*gql_model.UserPositionResponse, error) {
	return r.TradingResolver.GetUserPosition(ctx, input)
}

// GetUserTradeHistory is the resolver for the getUserTradeHistory field.
func (r *queryResolver) GetUserTradeHistory(ctx context.Context, input gql_model.UserTradeHistoryRequest) (*gql_model.UserTradeHistoryResponse, error) {
	return r.TradingResolver.GetUserTradeHistory(ctx, input)
}

// GetUserPrevDayBalance is the resolver for the getUserPrevDayBalance field.
func (r *queryResolver) GetUserPrevDayBalance(ctx context.Context, input gql_model.UserPrevDayBalanceRequest) (*gql_model.UserPrevDayBalanceResponse, error) {
	return r.TradingResolver.GetUserPrevDayBalance(ctx, input)
}

// GetUserOrders is the resolver for the getUserOrders field.
func (r *queryResolver) GetUserOrders(ctx context.Context, input gql_model.UserOrderStatusRequest) (*gql_model.UserOrderStatusResponse, error) {
	return r.OrderStatusResolver.GetUserOrders(ctx, input)
}

// GetUserMiscEvents is the resolver for the getUserMiscEvents field.
func (r *queryResolver) GetUserMiscEvents(ctx context.Context, input gql_model.UserMiscEventRequest) (*gql_model.UserMiscEventResponse, error) {
	return r.MiscEventResolver.GetUserEvents(ctx, input)
}

// GetUserNodeTrades is the resolver for the getUserNodeTrades field.
func (r *queryResolver) GetUserNodeTrades(ctx context.Context, input gql_model.UserNodeTradesRequest) (*gql_model.RespGetUserNodeTrades, error) {
	return r.NodeTradesResolver.GetUserNodeTrades(ctx, input)
}

// CallbackUserBalance is the resolver for the callbackUserBalance field.
func (r *queryResolver) CallbackUserBalance(ctx context.Context, input gql_model.CallbackUserBalanceRequest) (*gql_model.CallbackUserBalanceResponse, error) {
	return r.AssetResolver.CallbackUserBalance(ctx, input)
}

// GenerateCloid is the resolver for the generateCloid field.
func (r *queryResolver) GenerateCloid(ctx context.Context, input *gql_model.GenerateCloidInput) (*gql_model.GenerateCloidResponse, error) {
	return r.VerifyResolver.GenerateCloid(ctx, input)
}

// Mutation returns MutationResolver implementation.
func (r *Resolver) Mutation() MutationResolver { return &mutationResolver{r} }

// Query returns QueryResolver implementation.
func (r *Resolver) Query() QueryResolver { return &queryResolver{r} }

type mutationResolver struct{ *Resolver }
type queryResolver struct{ *Resolver }
