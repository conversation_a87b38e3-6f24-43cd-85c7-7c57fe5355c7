package graphql

import (
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/controller/graphql/resolvers"
)

// This file will not be regenerated automatically.
//
// It serves as dependency injection for your app, add any dependencies you require here.

type Resolver struct {
	SwapResolver        *resolvers.SwapResolver
	SymbolResolver      *resolvers.SymbolResolver
	TradingResolver     *resolvers.TradingResolver
	AssetResolver       *resolvers.AssetResolver
	OrderStatusResolver *resolvers.OrderStatusResolver
	NodeTradesResolver  *resolvers.NodeTradesResolver
	MiscEventResolver   *resolvers.MiscEventResolver
	VerifyResolver      *resolvers.VerifyResolver
}

func NewRootResolver() *Resolver {
	return &Resolver{
		SwapResolver:        resolvers.NewSwapResolver(),
		SymbolResolver:      resolvers.NewSymbolResolver(),
		TradingResolver:     resolvers.NewTradingResolver(),
		AssetResolver:       resolvers.NewAssetResolver(),
		OrderStatusResolver: resolvers.NewOrderStatusResolver(),
		NodeTradesResolver:  resolvers.NewNodeTradesResolver(),
		MiscEventResolver:   resolvers.NewMiscEventResolver(),
		VerifyResolver:      resolvers.NewVerifyResolver(),
	}
}
