// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package gql_model

import (
	"fmt"
	"io"
	"strconv"
)

type AlertSymbolSetting struct {
	SettingID      string               `json:"settingId"`
	Symbol         string               `json:"symbol"`
	Type           NotificationTypeEnum `json:"type"`
	TriggerValue   float64              `json:"triggerValue"`
	IsReminderOnce bool                 `json:"isReminderOnce"`
	IsActivated    bool                 `json:"isActivated"`
}

type Asset struct {
	Blockchain string `json:"blockchain"`
	Symbol     string `json:"symbol"`
	Address    string `json:"address"`
}

type BridgeExtra struct {
	RequireRefundAction bool   `json:"requireRefundAction"`
	SrcTx               string `json:"srcTx"`
	DestTx              string `json:"destTx"`
}

type CallBackRequest struct {
	RequestID string `json:"requestId"`
	Step      int    `json:"step"`
	TxHash    string `json:"txHash"`
}

type CallBackResponse struct {
	Success string `json:"success"`
}

type CallbackUserBalanceRequest struct {
	WalletAddress string `json:"walletAddress"`
}

type CallbackUserBalanceResponse struct {
	Error  *string `json:"error,omitempty"`
	Status string  `json:"status"`
}

type CategoryResponse struct {
	Categories []*string `json:"categories"`
}

type Chain struct {
	ChainID    string     `json:"chainId"`
	ChainImage string     `json:"chainImage"`
	ChainName  string     `json:"chainName"`
	Tokens     []*TokenV2 `json:"tokens"`
}

type CheckApprovalRequest struct {
	RequestID string `json:"requestId"`
	TxHash    string `json:"txHash"`
}

type CheckApprovalResponse struct {
	IsApproved             bool   `json:"isApproved"`
	TxStatus               string `json:"txStatus"`
	CurrentApprovedAmount  string `json:"currentApprovedAmount"`
	RequiredApprovedAmount string `json:"requiredApprovedAmount"`
}

type CheckStatusRequest struct {
	RequestID string `json:"requestId"`
	TxHash    string `json:"txHash"`
	Step      int    `json:"step"`
}

type CheckStatusResponse struct {
	Status       string          `json:"status"`
	ExtraMessage *string         `json:"extraMessage,omitempty"`
	FailedType   *string         `json:"failedType,omitempty"`
	Timestamp    int             `json:"timestamp"`
	OutputAmount string          `json:"outputAmount"`
	ExplorerURL  []*ExplorerInfo `json:"explorerUrl,omitempty"`
	Referrals    []*Referral     `json:"referrals,omitempty"`
	NewTx        *CheckStatusTx  `json:"newTx,omitempty"`
	DiagnosisURL *string         `json:"diagnosisUrl,omitempty"`
	Steps        *string         `json:"steps,omitempty"`
	OutputToken  *OutputToken    `json:"outputToken"`
	OutputType   string          `json:"outputType"`
	BridgeExtra  *BridgeExtra    `json:"bridgeExtra"`
	Error        *string         `json:"error,omitempty"`
	ErrorCode    *int            `json:"errorCode,omitempty"`
	TraceID      *int            `json:"traceId,omitempty"`
}

type CheckStatusTx struct {
	Data     string `json:"data"`
	To       string `json:"to"`
	Value    string `json:"value"`
	GasLimit string `json:"gasLimit"`
	GasPrice string `json:"gasPrice"`
	Nonce    string `json:"nonce"`
}

type ConfirmRouteRequest struct {
	SelectedWallets []*SelectedWallet `json:"selectedWallets"`
	Destination     string            `json:"destination"`
	RequestID       string            `json:"requestId"`
}

type ConfirmRouteResponse struct {
	RequestID string  `json:"requestId"`
	Status    string  `json:"status"`
	RouteErr  *string `json:"routeErr,omitempty"`
}

type CreateAlertSymbolSettingRequest struct {
	Symbol         string               `json:"symbol"`
	Type           NotificationTypeEnum `json:"type"`
	Value          float64              `json:"value"`
	IsReminderOnce bool                 `json:"isReminderOnce"`
	Note           *string              `json:"note,omitempty"`
}

type CreateAlertSymbolSettingResponse struct {
	Error     *string `json:"error,omitempty"`
	Status    string  `json:"status"`
	SettingID string  `json:"settingId"`
}

type CreateTxRequest struct {
	RequestID    string               `json:"requestId"`
	Step         int                  `json:"step"`
	UserSettings *UserSettingsRequest `json:"userSettings"`
	Validations  *ValidationsRequest  `json:"validations"`
}

type CreateTxResponse struct {
	Ok                     bool                            `json:"ok"`
	Error                  *string                         `json:"error,omitempty"`
	ErrorCode              int                             `json:"errorCode"`
	TraceID                int                             `json:"traceId"`
	SignUserTransactionEvm *SignUserTransactionEvmResponse `json:"signUserTransactionEvm"`
	Transaction            *Transaction                    `json:"transaction,omitempty"`
}

type DeleteAlertSymbolSettingRequest struct {
	SettingIds []string `json:"settingIds"`
}

type DeleteAlertSymbolSettingResponse struct {
	Error  *string `json:"error,omitempty"`
	Status string  `json:"status"`
}

type ExchangeMetaResponse struct {
	Chains []*Chain `json:"chains"`
}

type ExplorerInfo struct {
	URL         string `json:"url"`
	Description string `json:"description"`
}

type FavoriteSymbolsResponse struct {
	List []*Symbol `json:"list"`
}

type Fee struct {
	Asset       *Asset  `json:"asset"`
	ExpenseType string  `json:"expenseType"`
	Amount      string  `json:"amount"`
	Name        string  `json:"name"`
	Meta        *Meta   `json:"meta,omitempty"`
	Price       float64 `json:"price"`
}

type FromTo struct {
	Blockchain string `json:"blockchain"`
	Symbol     string `json:"symbol"`
	Address    string `json:"address"`
}

type GenerateCloidInput struct {
	Count *int `json:"count,omitempty"`
}

type GenerateCloidResponse struct {
	Cloids []string `json:"cloids"`
	Count  int      `json:"count"`
}

type GetAlertSymbolSettingResponse struct {
	Settings []*AlertSymbolSetting `json:"settings"`
}

type GetAllPossibleRoutesRequest struct {
	FromBlockchain   string  `json:"fromBlockchain"`
	FromSymbol       string  `json:"fromSymbol"`
	FromTokenAddress *string `json:"fromTokenAddress,omitempty"`
	ToBlockchain     string  `json:"toBlockchain"`
	ToSymbol         string  `json:"toSymbol"`
	ToTokenAddress   *string `json:"toTokenAddress,omitempty"`
	Amount           string  `json:"amount"`
	Slippage         string  `json:"slippage"`
}

type GetAllPossibleRoutesResponse struct {
	From              *FromTo   `json:"from"`
	To                *FromTo   `json:"to"`
	RequestAmount     string    `json:"requestAmount"`
	RouteID           string    `json:"routeId"`
	Results           []*Result `json:"results"`
	DiagnosisMessages []string  `json:"diagnosisMessages"`
	Error             *string   `json:"error,omitempty"`
	ErrorCode         int       `json:"errorCode"`
	TraceID           int       `json:"traceId"`
}

type GetBestRouteRequest struct {
	FromBlockChain   string  `json:"fromBlockChain"`
	FromSymbol       string  `json:"fromSymbol"`
	FromAddress      string  `json:"fromAddress"`
	FromTokenAddress *string `json:"fromTokenAddress,omitempty"`
	ToBlockChain     string  `json:"toBlockChain"`
	ToSymbol         string  `json:"toSymbol"`
	ToTokenAddress   *string `json:"toTokenAddress,omitempty"`
	ToAddress        string  `json:"toAddress"`
	Amount           string  `json:"amount"`
}

type GetBestRouteResponse struct {
	From                              *FromTo  `json:"from"`
	To                                *FromTo  `json:"to"`
	RequestAmount                     string   `json:"requestAmount"`
	RequestID                         string   `json:"requestId"`
	Result                            *Result  `json:"result"`
	ValidationStatus                  string   `json:"validationStatus"`
	WalletNotSupportingFromBlockchain bool     `json:"walletNotSupportingFromBlockchain"`
	MissingBlockchains                []string `json:"missingBlockchains"`
	DiagnosisMessages                 []string `json:"diagnosisMessages"`
	Error                             *string  `json:"error,omitempty"`
	ErrorCode                         *string  `json:"errorCode,omitempty"`
	TraceID                           *string  `json:"traceId,omitempty"`
}

type History struct {
	ID                                string        `json:"id"`
	RequestID                         string        `json:"requestId"`
	UserAddress                       string        `json:"userAddress"`
	FromBlockchain                    string        `json:"fromBlockchain"`
	FromSymbol                        string        `json:"fromSymbol"`
	FromAddress                       string        `json:"fromAddress"`
	ToBlockchain                      string        `json:"toBlockchain"`
	ToSymbol                          string        `json:"toSymbol"`
	ToAddress                         string        `json:"toAddress"`
	RequestAmount                     string        `json:"requestAmount"`
	OutputAmount                      string        `json:"outputAmount"`
	ResultType                        string        `json:"resultType"`
	ValidationStatus                  string        `json:"validationStatus"`
	WalletNotSupportingFromBlockchain bool          `json:"walletNotSupportingFromBlockchain"`
	MissingBlockchains                *string       `json:"missingBlockchains,omitempty"`
	DiagnosisMessages                 *string       `json:"diagnosisMessages,omitempty"`
	Status                            string        `json:"status"`
	Step                              int           `json:"step"`
	FailReason                        *string       `json:"failReason,omitempty"`
	Swaps                             []*RangoSwaps `json:"swaps,omitempty"`
}

type HistoryRequest struct {
	Address    string  `json:"address"`
	Blockchain string  `json:"blockchain"`
	Page       int     `json:"page"`
	PageSize   int     `json:"pageSize"`
	StartTime  *int    `json:"startTime,omitempty"`
	EndTime    *int    `json:"endTime,omitempty"`
	Status     *string `json:"status,omitempty"`
}

type HistoryResponse struct {
	Total int        `json:"total"`
	List  []*History `json:"list"`
}

type Instruction struct {
	Keys      []*InstructionKey `json:"keys"`
	ProgramID string            `json:"programId"`
	Data      string            `json:"data"`
}

type InstructionKey struct {
	Pubkey     string `json:"pubkey"`
	IsSigner   bool   `json:"isSigner"`
	IsWritable bool   `json:"isWritable"`
}

type InternalSwaps struct {
	SwapperID                 string           `json:"swapperId"`
	SwapperLogo               string           `json:"swapperLogo"`
	SwapperType               string           `json:"swapperType"`
	From                      *TokenInfo       `json:"from"`
	To                        *TokenInfo       `json:"to"`
	FromAmount                string           `json:"fromAmount"`
	FromAmountPrecision       string           `json:"fromAmountPrecision"`
	FromAmountMinValue        *string          `json:"fromAmountMinValue,omitempty"`
	FromAmountMaxValue        *string          `json:"fromAmountMaxValue,omitempty"`
	FromAmountRestrictionType *string          `json:"fromAmountRestrictionType,omitempty"`
	ToAmount                  string           `json:"toAmount"`
	Fee                       []*Fee           `json:"fee"`
	EstimatedTimeInSeconds    int              `json:"estimatedTimeInSeconds"`
	SwapChainType             string           `json:"swapChainType"`
	Routes                    []*Route         `json:"routes"`
	RecommendedSlippage       string           `json:"recommendedSlippage"`
	Warnings                  string           `json:"warnings"`
	TimeStat                  string           `json:"timeStat"`
	IncludesDestinationTx     bool             `json:"includesDestinationTx"`
	InternalSwaps             []*InternalSwaps `json:"internalSwaps"`
	MaxRequiredSign           int              `json:"maxRequiredSign"`
}

type Leverage struct {
	Type  string `json:"type"`
	Value int    `json:"value"`
}

type LogTransactionInput struct {
	Oid           *string      `json:"oid,omitempty"`
	BaseCoin      *string      `json:"baseCoin,omitempty"`
	Side          *Side        `json:"side,omitempty"`
	Price         *string      `json:"price,omitempty"`
	Size          *string      `json:"size,omitempty"`
	OrderType     *OrderType   `json:"orderType,omitempty"`
	Operation     *Operation   `json:"operation,omitempty"`
	OrderStatus   *OrderStatus `json:"orderStatus,omitempty"`
	Cloid         *string      `json:"cloid,omitempty"`
	AvgPx         *string      `json:"avgPx,omitempty"`
	TotalSz       *string      `json:"totalSz,omitempty"`
	Grouping      *Grouping    `json:"grouping,omitempty"`
	Created       *string      `json:"created,omitempty"`
	WalletAddress *string      `json:"walletAddress,omitempty"`
}

type LogTransactionResponse struct {
	Error  *string `json:"error,omitempty"`
	Status string  `json:"status"`
}

type Meta struct {
	Type     string `json:"type"`
	GasLimit string `json:"gasLimit"`
	GasPrice string `json:"gasPrice"`
}

type Mutation struct {
}

type NewSymbolResponse struct {
	List []*string `json:"list"`
}

type Node struct {
	InputAmount  string   `json:"inputAmount"`
	MarketID     string   `json:"marketId"`
	MarketName   string   `json:"marketName"`
	OutputAmount string   `json:"outputAmount"`
	Percent      float64  `json:"percent"`
	Pools        []string `json:"pools"`
}

type NodeTrades struct {
	Coin             string           `json:"coin"`
	Side             string           `json:"side"`
	Time             string           `json:"time"`
	Px               string           `json:"px"`
	Sz               string           `json:"sz"`
	Hash             string           `json:"hash"`
	TradeDirOverride string           `json:"tradeDirOverride"`
	SideInfoTrade    []*SideInfoTrade `json:"sideInfoTrade"`
}

type Nodes struct {
	Nodes          []*Node `json:"nodes"`
	From           string  `json:"from"`
	FromLogo       string  `json:"fromLogo"`
	FromAddress    string  `json:"fromAddress"`
	FromBlockchain string  `json:"fromBlockchain"`
	To             string  `json:"to"`
	ToLogo         string  `json:"toLogo"`
	ToAddress      string  `json:"toAddress"`
	ToBlockchain   string  `json:"toBlockchain"`
}

type Ohlc struct {
	Timestamp int     `json:"timestamp"`
	Open      float64 `json:"open"`
	Close     float64 `json:"close"`
	High      float64 `json:"high"`
	Low       float64 `json:"low"`
	Volume    float64 `json:"volume"`
}

type OHLCRequest struct {
	Symbol    string           `json:"symbol"`
	Interval  OHLCIntervalEnum `json:"interval"`
	Timestamp int              `json:"timestamp"`
	IsForward bool             `json:"isForward"`
	Limit     int              `json:"limit"`
}

type OHLCResponse struct {
	Symbol string  `json:"symbol"`
	Data   []*Ohlc `json:"data"`
}

type OpenOrder struct {
	Time       int     `json:"time"`
	Type       string  `json:"type"`
	Symbol     string  `json:"symbol"`
	Direction  string  `json:"direction"`
	Size       float64 `json:"size"`
	OriginSize float64 `json:"originSize"`
	OrderPx    float64 `json:"orderPx"`
	TriggerPx  float64 `json:"triggerPx"`
	ReduceOnly bool    `json:"reduceOnly"`
}

type OutputToken struct {
	Blockchain        string   `json:"blockchain"`
	Symbol            string   `json:"symbol"`
	Image             string   `json:"image"`
	Address           string   `json:"address"`
	UsdPrice          float64  `json:"usdPrice"`
	Decimals          int      `json:"decimals"`
	Name              *string  `json:"name,omitempty"`
	IsPopular         bool     `json:"isPopular"`
	IsSecondaryCoin   bool     `json:"isSecondaryCoin"`
	CoinSource        *string  `json:"coinSource,omitempty"`
	CoinSourceURL     *string  `json:"coinSourceUrl,omitempty"`
	SupportedSwappers []string `json:"supportedSwappers"`
}

type PopularSymbolRequest struct {
	Number int `json:"number"`
}

type PopularSymbolResponse struct {
	List []*Symbol `json:"list"`
}

type Position struct {
	Symbol     string    `json:"symbol"`
	Size       float64   `json:"size"`
	Leverage   *Leverage `json:"leverage"`
	Side       int       `json:"side"`
	EntryPx    float64   `json:"entryPx"`
	FundingFee float64   `json:"fundingFee"`
}

type Query struct {
}

type QuoteRequest struct {
	User          string  `json:"user"`
	OriginID      string  `json:"originId"`
	DestinationID string  `json:"destinationId"`
	Recipient     string  `json:"recipient"`
	Amount        string  `json:"amount"`
	Type          string  `json:"type"`
	RequestID     *string `json:"requestId,omitempty"`
}

type QuoteResponse struct {
	Items                   []*StepItem `json:"items"`
	RequestID               string      `json:"requestId"`
	Type                    string      `json:"type"`
	Description             string      `json:"description"`
	ErrorCode               string      `json:"errorCode"`
	OutPutAmountFormatted   string      `json:"outPutAmountFormatted"`
	GasTopupAmount          string      `json:"gasTopupAmount"`
	GasTopupAmountFormatted string      `json:"gasTopupAmountFormatted"`
	GasTopupAmountUsd       string      `json:"gasTopupAmountUsd"`
	GasAmountFormatted      string      `json:"gasAmountFormatted"`
	PlatformFeeAmountFormat string      `json:"platformFeeAmountFormat"`
	PlatformFeeSymbol       string      `json:"platformFeeSymbol"`
}

type RangoHistory struct {
	ID                                string        `json:"id"`
	RequestID                         string        `json:"requestId"`
	UserAddress                       string        `json:"userAddress"`
	FromBlockchain                    string        `json:"fromBlockchain"`
	FromSymbol                        string        `json:"fromSymbol"`
	FromAddress                       string        `json:"fromAddress"`
	ToBlockchain                      string        `json:"toBlockchain"`
	ToSymbol                          string        `json:"toSymbol"`
	ToAddress                         string        `json:"toAddress"`
	RequestAmount                     string        `json:"requestAmount"`
	OutputAmount                      string        `json:"outputAmount"`
	ResultType                        string        `json:"resultType"`
	ValidationStatus                  string        `json:"validationStatus"`
	WalletNotSupportingFromBlockchain bool          `json:"walletNotSupportingFromBlockchain"`
	MissingBlockchains                *string       `json:"missingBlockchains,omitempty"`
	DiagnosisMessages                 *string       `json:"diagnosisMessages,omitempty"`
	Status                            string        `json:"status"`
	Step                              int           `json:"step"`
	FailReason                        *string       `json:"failReason,omitempty"`
	Swaps                             []*RangoSwaps `json:"swaps,omitempty"`
}

type RangoHistoryRequest struct {
	Address    string  `json:"address"`
	Blockchain string  `json:"blockchain"`
	Page       int     `json:"page"`
	PageSize   int     `json:"pageSize"`
	StartTime  *int    `json:"startTime,omitempty"`
	EndTime    *int    `json:"endTime,omitempty"`
	Status     *string `json:"status,omitempty"`
}

type RangoHistoryResponse struct {
	Total int             `json:"total"`
	List  []*RangoHistory `json:"list"`
}

type RangoMetaChains struct {
	ID              string  `json:"id"`
	Name            string  `json:"name"`
	ChainID         *string `json:"chainId,omitempty"`
	DefaultDecimals *int    `json:"defaultDecimals,omitempty"`
	AddressPatterns *string `json:"addressPatterns,omitempty"`
	FeeAssets       *string `json:"feeAssets,omitempty"`
	Logo            *string `json:"logo,omitempty"`
	DisplayName     *string `json:"displayName,omitempty"`
	ShortName       *string `json:"shortName,omitempty"`
	Sort            *int    `json:"sort,omitempty"`
	Color           *string `json:"color,omitempty"`
	Enabled         *bool   `json:"enabled,omitempty"`
	Type            *string `json:"type,omitempty"`
	Info            *string `json:"info,omitempty"`
}

type RangoMetaResponse struct {
	Tokens        []*RangoMetaTokensWithChain `json:"tokens,omitempty"`
	Swappers      []*RangoMetaSwappers        `json:"swappers,omitempty"`
	Blockchains   []*RangoMetaChains          `json:"blockchains,omitempty"`
	PopularTokens []*RangoMetaTokens          `json:"popularTokens,omitempty"`
}

type RangoMetaSwappers struct {
	ID           string  `json:"id"`
	SwapperID    string  `json:"swapperId"`
	Title        *string `json:"title,omitempty"`
	Logo         *string `json:"logo,omitempty"`
	SwapperGroup *string `json:"swapperGroup,omitempty"`
	Types        *string `json:"types,omitempty"`
	Enabled      *bool   `json:"enabled,omitempty"`
}

type RangoMetaTokens struct {
	ID                string    `json:"id"`
	Address           string    `json:"address"`
	BlockChain        string    `json:"blockChain"`
	Symbol            string    `json:"symbol"`
	Name              *string   `json:"name,omitempty"`
	Image             *string   `json:"image,omitempty"`
	UsdPrice          *float64  `json:"usdPrice,omitempty"`
	Decimals          *int      `json:"decimals,omitempty"`
	IsPopular         *bool     `json:"isPopular,omitempty"`
	IsSecondaryCoin   *bool     `json:"isSecondaryCoin,omitempty"`
	CoinSource        *string   `json:"coinSource,omitempty"`
	CoinSourceURL     *string   `json:"coinSourceUrl,omitempty"`
	SupportedSwappers []*string `json:"supportedSwappers,omitempty"`
}

type RangoMetaTokensWithChain struct {
	ID                string           `json:"id"`
	Address           string           `json:"address"`
	BlockChain        string           `json:"blockChain"`
	Symbol            string           `json:"symbol"`
	Name              *string          `json:"name,omitempty"`
	Image             *string          `json:"image,omitempty"`
	UsdPrice          *float64         `json:"usdPrice,omitempty"`
	Decimals          *int             `json:"decimals,omitempty"`
	IsPopular         *bool            `json:"isPopular,omitempty"`
	IsSecondaryCoin   *bool            `json:"isSecondaryCoin,omitempty"`
	CoinSource        *string          `json:"coinSource,omitempty"`
	CoinSourceURL     *string          `json:"coinSourceUrl,omitempty"`
	SupportedSwappers []*string        `json:"supportedSwappers,omitempty"`
	ChainDetail       *RangoMetaChains `json:"chainDetail,omitempty"`
}

type RangoSwaps struct {
	ID                     string  `json:"id"`
	RequestID              string  `json:"requestId"`
	UserAddress            string  `json:"userAddress"`
	SwapperID              string  `json:"swapperId"`
	SwapperLogo            *string `json:"swapperLogo,omitempty"`
	SwapperType            string  `json:"swapperType"`
	FromAmount             string  `json:"fromAmount"`
	ToAmount               string  `json:"toAmount"`
	Fee                    *string `json:"fee,omitempty"`
	EstimatedTimeInSeconds int     `json:"estimatedTimeInSeconds"`
	SwapChainType          string  `json:"swapChainType"`
	MaxRequiredSign        int     `json:"maxRequiredSign"`
	Step                   int     `json:"step"`
	CallData               *string `json:"callData,omitempty"`
	CallDataHash           *string `json:"callDataHash,omitempty"`
	TxHash                 *string `json:"txHash,omitempty"`
}

type RangoTokenExtra struct {
	ID string `json:"id"`
}

type RecommendedSlippage struct {
	Error    bool   `json:"error"`
	Slippage string `json:"slippage"`
}

type Referral struct {
	Amount     string  `json:"amount"`
	BlockChain string  `json:"blockChain"`
	Symbol     string  `json:"symbol"`
	Address    *string `json:"address,omitempty"`
	Decimals   int     `json:"decimals"`
	Type       string  `json:"type"`
}

type RelayTokenExtra struct {
	ID string `json:"id"`
}

type RespGetUserNodeTrades struct {
	Orders []*NodeTrades `json:"orders"`
}

type Result struct {
	RequestID                         string   `json:"requestId"`
	OutputAmount                      string   `json:"outputAmount"`
	Swaps                             []*Swap  `json:"swaps"`
	ResultType                        string   `json:"resultType"`
	Scores                            []*Score `json:"scores"`
	Tags                              []*Tag   `json:"tags"`
	WalletNotSupportingFromBlockchain bool     `json:"walletNotSupportingFromBlockchain"`
	MissingBlockchains                []string `json:"missingBlockchains"`
	PriceImpactUsd                    string   `json:"priceImpactUsd"`
	PriceImpactUsdPercent             string   `json:"priceImpactUsdPercent"`
}

type Route struct {
	Nodes []*Nodes `json:"nodes"`
}

type Score struct {
	PreferenceType string `json:"preferenceType"`
	Score          int    `json:"score"`
}

type SearchSymbolRequest struct {
	Filter string `json:"filter"`
}

type SearchSymbolResponse struct {
	List []*Symbol `json:"list"`
}

type SelectedWallet struct {
	Blockchain string `json:"blockchain"`
	Address    string `json:"address"`
}

type SideInfoTrade struct {
	User     string  `json:"user"`
	StartPos string  `json:"startPos"`
	Oid      *int    `json:"oid,omitempty"`
	TwapID   *int    `json:"twapId,omitempty"`
	Cloid    *string `json:"cloid,omitempty"`
}

type SignTxResponse struct {
	SignedTransaction    string `json:"signedTransaction"`
	From                 string `json:"from"`
	To                   string `json:"to"`
	Data                 string `json:"data"`
	Value                string `json:"value"`
	GasLimit             string `json:"gasLimit"`
	GasPrice             string `json:"gasPrice"`
	MaxPriorityFeePerGas string `json:"maxPriorityFeePerGas"`
	MaxFeePerGas         string `json:"maxFeePerGas"`
	Nonce                string `json:"nonce"`
	BlockChain           string `json:"blockChain"`
}

type SignUserTransactionEvmResponse struct {
	UserID               string    `json:"user_id"`
	To                   string    `json:"to"`
	Data                 string    `json:"data"`
	Value                *string   `json:"value,omitempty"`
	Chain                ChainType `json:"chain"`
	MaxFeePerGas         *string   `json:"maxFeePerGas,omitempty"`
	MaxPriorityFeePerGas *string   `json:"maxPriorityFeePerGas,omitempty"`
	GasLimit             *string   `json:"gasLimit,omitempty"`
	GasPrice             *string   `json:"gasPrice,omitempty"`
	From                 string    `json:"from"`
	SignedTransaction    string    `json:"signed_transaction"`
}

type Signal struct {
	Username           string  `json:"username"`
	UUID               string  `json:"uuid"`
	SignalType         string  `json:"signalType"`
	SignalName         string  `json:"signalName"`
	SignalPnl          float64 `json:"signalPnl"`
	InitialCapital     float64 `json:"initialCapital"`
	LatestAssets       float64 `json:"latestAssets"`
	RunningStatus      string  `json:"runningStatus"`
	ConsecutiveWins    int     `json:"consecutiveWins"`
	Subscribers        int     `json:"subscribers"`
	OperationDirection string  `json:"operationDirection"`
	UnderlyingAsset    string  `json:"underlyingAsset"`
	MonthlyReturnRate  float64 `json:"monthlyReturnRate"`
	MonthlyAlpha       float64 `json:"monthlyAlpha"`
	AnnualWinRate      float64 `json:"annualWinRate"`
	CumulativeIncome   float64 `json:"cumulativeIncome"`
	SharpeRatio        float64 `json:"sharpeRatio"`
	ThreeYield         float64 `json:"threeYield"`
	SevenYield         float64 `json:"sevenYield"`
	MaxDrawdown7Days   float64 `json:"maxDrawdown7Days"`
	RunningTime        float64 `json:"runningTime"`
	HistoricalWinRate  float64 `json:"historicalWinRate"`
	ProfitLossCount    float64 `json:"profitLossCount"`
	ProfitLossRatio    float64 `json:"profitLossRatio"`
	Confidence         string  `json:"confidence"`
	EvaluationStatus   string  `json:"evaluationStatus"`
	Review             string  `json:"review"`
	UserID             int     `json:"userId"`
	CreatedDate        string  `json:"createdDate"`
	UpdatedDate        string  `json:"updatedDate"`
}

type SignalRequest struct {
	Limit *int    `json:"limit,omitempty"`
	Skip  *int    `json:"skip,omitempty"`
	User  *string `json:"user,omitempty"`
}

type SignalResponse struct {
	Total   int       `json:"total"`
	Signals []*Signal `json:"signals"`
}

type StepItem struct {
	From                   string         `json:"from"`
	To                     string         `json:"to"`
	Data                   string         `json:"data"`
	Value                  string         `json:"value"`
	ValueAmountUsd         string         `json:"valueAmountUsd"`
	ValueAmountFormatted   string         `json:"valueAmountFormatted"`
	ChainID                string         `json:"chainId"`
	Gas                    string         `json:"gas"`
	GasAmountUsd           string         `json:"gasAmountUsd"`
	GasAmountFormatted     string         `json:"gasAmountFormatted"`
	MaxFeePerGas           string         `json:"maxFeePerGas"`
	MaxPriorityFeePerGas   string         `json:"maxPriorityFeePerGas"`
	Instructions           []*Instruction `json:"instructions,omitempty"`
	SerializedMessage      []int          `json:"serializedMessage,omitempty"`
	Nonce                  string         `json:"nonce"`
	BlockHash              string         `json:"blockHash"`
	TransactionType        string         `json:"transactionType"`
	EstimatedTimeInSeconds int            `json:"estimatedTimeInSeconds"`
	IsApprovalTx           bool           `json:"isApprovalTx"`
}

type StoreTxInformationRequest struct {
	TxType       string   `json:"txType"`
	Symbol       string   `json:"symbol"`
	IsBuy        bool     `json:"isBuy"`
	Price        float64  `json:"price"`
	Size         float64  `json:"size"`
	ReduceOnly   bool     `json:"reduceOnly"`
	OrderType    string   `json:"orderType"`
	Nonce        int      `json:"nonce"`
	VaultAddress *string  `json:"vaultAddress,omitempty"`
	ExpiresAfter *int     `json:"expiresAfter,omitempty"`
	Oid          string   `json:"oid"`
	AvgPx        *float64 `json:"avgPx,omitempty"`
	Direction    string   `json:"direction"`
	Status       string   `json:"status"`
}

type StoreTxInformationResponse struct {
	Error  *string `json:"error,omitempty"`
	Status string  `json:"status"`
}

type Swap struct {
	SwapperID                 string               `json:"swapperId"`
	SwapperLogo               string               `json:"swapperLogo"`
	SwapperType               string               `json:"swapperType"`
	From                      *TokenInfo           `json:"from"`
	To                        *TokenInfo           `json:"to"`
	FromAmount                string               `json:"fromAmount"`
	FromAmountPrecision       string               `json:"fromAmountPrecision"`
	FromAmountMinValue        *string              `json:"fromAmountMinValue,omitempty"`
	FromAmountMaxValue        *string              `json:"fromAmountMaxValue,omitempty"`
	FromAmountRestrictionType *string              `json:"fromAmountRestrictionType,omitempty"`
	ToAmount                  string               `json:"toAmount"`
	Fee                       []*Fee               `json:"fee"`
	EstimatedTimeInSeconds    int                  `json:"estimatedTimeInSeconds"`
	SwapChainType             string               `json:"swapChainType"`
	Routes                    []*Route             `json:"routes"`
	RecommendedSlippage       *RecommendedSlippage `json:"recommendedSlippage"`
	Warnings                  []string             `json:"warnings"`
	TimeStat                  *TimeStat            `json:"timeStat"`
	IncludesDestinationTx     bool                 `json:"includesDestinationTx"`
	InternalSwaps             []*InternalSwaps     `json:"internalSwaps"`
	MaxRequiredSign           int                  `json:"maxRequiredSign"`
}

type Symbol struct {
	Symbol         string  `json:"symbol"`
	MaxLeverage    int     `json:"maxLeverage"`
	MarketCap      float64 `json:"marketCap"`
	Volume         float64 `json:"volume"`
	ChangPxPercent float64 `json:"changPxPercent"`
	OpenInterest   float64 `json:"openInterest"`
	CurrentPrice   float64 `json:"currentPrice"`
}

type SymbolDetailRequest struct {
	Symbol string `json:"symbol"`
}

type SymbolDetailResponse struct {
	Symbol         string  `json:"symbol"`
	MaxLeverage    int     `json:"maxLeverage"`
	OnlyIsolated   bool    `json:"onlyIsolated"`
	SizeDecimals   int     `json:"sizeDecimals"`
	MarginTableID  int     `json:"marginTableID"`
	MarketCap      float64 `json:"marketCap"`
	High           float64 `json:"high"`
	Low            float64 `json:"low"`
	Volume         float64 `json:"volume"`
	ChangPxPercent float64 `json:"changPxPercent"`
	CurrentPrice   float64 `json:"currentPrice"`
}

type SymbolListRequest struct {
	Condition SymbolConditionEnum `json:"condition"`
	Category  *string             `json:"category,omitempty"`
}

type SymbolListResponse struct {
	List []*Symbol `json:"list"`
}

type Tag struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

type TimeStat struct {
	Min int `json:"min"`
	Avg int `json:"avg"`
	Max int `json:"max"`
}

type Token struct {
	Address           string   `json:"address"`
	BlockChain        string   `json:"blockChain"`
	Symbol            string   `json:"symbol"`
	Name              string   `json:"name"`
	Image             *string  `json:"image,omitempty"`
	UsdPrice          *float64 `json:"usdPrice,omitempty"`
	Decimals          *int     `json:"decimals,omitempty"`
	IsPopular         *bool    `json:"isPopular,omitempty"`
	IsSecondaryCoin   *bool    `json:"isSecondaryCoin,omitempty"`
	CoinSource        *string  `json:"coinSource,omitempty"`
	CoinSourceURL     *string  `json:"coinSourceUrl,omitempty"`
	SupportedSwappers []string `json:"supportedSwappers,omitempty"`
}

type TokenInfo struct {
	Symbol         string  `json:"symbol"`
	Logo           string  `json:"logo"`
	BlockchainLogo string  `json:"blockchainLogo"`
	Address        string  `json:"address"`
	Blockchain     string  `json:"blockchain"`
	Decimals       int     `json:"decimals"`
	UsdPrice       float64 `json:"usdPrice"`
}

type TokenV2 struct {
	Address    string           `json:"address"`
	Symbol     string           `json:"symbol"`
	Name       string           `json:"name"`
	Image      string           `json:"image"`
	Decimals   int              `json:"decimals"`
	UsdPrice   float64          `json:"usdPrice"`
	RelayExtra *RelayTokenExtra `json:"relayExtra,omitempty"`
	RangoExtra *RangoTokenExtra `json:"rangoExtra,omitempty"`
}

type TradeHistory struct {
	Symbol        string  `json:"symbol"`
	Time          int     `json:"time"`
	Pnl           float64 `json:"pnl"`
	PnlPercent    float64 `json:"pnlPercent"`
	Dir           string  `json:"dir"`
	Hash          string  `json:"hash"`
	Oid           int     `json:"oid"`
	Px            float64 `json:"px"`
	StartPosition string  `json:"startPosition"`
	Sz            float64 `json:"sz"`
	Fee           float64 `json:"fee"`
	FeeToken      string  `json:"feeToken"`
	Tid           int     `json:"tid"`
}

type Transaction struct {
	Type                 string   `json:"type"`
	BlockChain           string   `json:"blockChain"`
	IsApprovalTx         bool     `json:"isApprovalTx"`
	From                 string   `json:"from"`
	To                   string   `json:"to"`
	Spender              string   `json:"spender"`
	Data                 string   `json:"data"`
	Value                string   `json:"value"`
	GasLimit             string   `json:"gasLimit"`
	GasPrice             string   `json:"gasPrice"`
	MaxPriorityFeePerGas string   `json:"maxPriorityFeePerGas"`
	MaxFeePerGas         string   `json:"maxFeePerGas"`
	Nonce                string   `json:"nonce"`
	Identifier           string   `json:"identifier"`
	Instructions         []string `json:"instructions"`
	RecentBlockhash      string   `json:"recentBlockhash"`
	Signatures           []string `json:"signatures"`
	SerializedMessage    []int    `json:"serializedMessage"`
	TxType               string   `json:"txType"`
}

type UpdateAlertSymbolSettingRequest struct {
	SettingID   string `json:"settingId"`
	IsActivated bool   `json:"isActivated"`
}

type UpdateAlertSymbolSettingResponse struct {
	Error  *string `json:"error,omitempty"`
	Status string  `json:"status"`
}

type UpdateUserSymbolPreferenceRequest struct {
	Leverage        *int    `json:"leverage,omitempty"`
	IsCross         *bool   `json:"isCross,omitempty"`
	OrderUnitInBase *bool   `json:"orderUnitInBase,omitempty"`
	TpslUnit        *string `json:"tpslUnit,omitempty"`
	Symbol          string  `json:"symbol"`
}

type UpsertFavoriteSymbolRequest struct {
	Symbol     []string `json:"symbol"`
	IsFavorite bool     `json:"isFavorite"`
}

type UpsertFavoriteSymbolResponse struct {
	Error  *string `json:"error,omitempty"`
	Status string  `json:"status"`
}

type UserMiscEvent struct {
	Time                   *string `json:"time,omitempty"`
	Hash                   *string `json:"hash,omitempty"`
	Users                  *string `json:"users,omitempty"`
	CDepositUser           *string `json:"cDepositUser,omitempty"`
	CDepositAmount         *string `json:"cDepositAmount,omitempty"`
	CWithdrawalUser        *string `json:"cWithdrawalUser,omitempty"`
	CWithdrawalAmount      *string `json:"cWithdrawalAmount,omitempty"`
	CWithdrawalIsFinalized *bool   `json:"cWithdrawalIsFinalized,omitempty"`
	Type                   *string `json:"type,omitempty"`
	Usdc                   *string `json:"usdc,omitempty"`
	ToPerp                 *bool   `json:"toPerp,omitempty"`
	Token                  *string `json:"token,omitempty"`
	Amount                 *string `json:"amount,omitempty"`
	UsdcValue              *string `json:"usdcValue,omitempty"`
	User                   *string `json:"user,omitempty"`
	UserID                 *string `json:"userId,omitempty"`
	Destination            *string `json:"destination,omitempty"`
	Fee                    *string `json:"fee,omitempty"`
	NativeTokenFee         *string `json:"nativeTokenFee,omitempty"`
	Nonce                  *int    `json:"nonce,omitempty"`
}

type UserMiscEventRequest struct {
	LastID string `json:"lastID"`
	Limit  int    `json:"limit"`
}

type UserMiscEventResponse struct {
	Events []*UserMiscEvent `json:"events"`
}

type UserNodeTradesRequest struct {
	LastID string `json:"lastID"`
	Limit  int    `json:"limit"`
}

type UserOpenOrderRequest struct {
	LastID string `json:"lastID"`
	Limit  int    `json:"limit"`
}

type UserOpenOrderResponse struct {
	OpenOrders []*OpenOrder `json:"openOrders"`
}

type UserOrderStatus struct {
	Oid              *int    `json:"oid,omitempty"`
	CreatedAt        *string `json:"createdAt,omitempty"`
	Time             *string `json:"time,omitempty"`
	User             *string `json:"user,omitempty"`
	Status           *string `json:"status,omitempty"`
	UserID           *string `json:"userId,omitempty"`
	Cloid            *string `json:"cloid,omitempty"`
	Coin             *string `json:"coin,omitempty"`
	Side             *string `json:"side,omitempty"`
	LimitPx          *string `json:"limitPx,omitempty"`
	Sz               *string `json:"sz,omitempty"`
	Timestamp        *int    `json:"timestamp,omitempty"`
	TriggerCondition *string `json:"triggerCondition,omitempty"`
	IsTrigger        *bool   `json:"isTrigger,omitempty"`
	TriggerPx        *string `json:"triggerPx,omitempty"`
	Children         *string `json:"children,omitempty"`
	IsPositionTpsl   *bool   `json:"isPositionTpsl,omitempty"`
	ReduceOnly       *bool   `json:"reduceOnly,omitempty"`
	OrderType        *string `json:"orderType,omitempty"`
	OrigSz           *string `json:"origSz,omitempty"`
	Tif              *string `json:"tif,omitempty"`
}

type UserOrderStatusRequest struct {
	LastID string `json:"lastID"`
	Limit  int    `json:"limit"`
}

type UserOrderStatusResponse struct {
	Statuses []*UserOrderStatus `json:"statuses"`
}

type UserPositionRequest struct {
	WalletAddress string `json:"walletAddress"`
}

type UserPositionResponse struct {
	RawUsd    float64     `json:"rawUSD"`
	Positions []*Position `json:"positions"`
}

type UserPrevDayBalanceRequest struct {
	WalletAddress string `json:"walletAddress"`
}

type UserPrevDayBalanceResponse struct {
	Balance float64 `json:"Balance"`
}

type UserSettingsRequest struct {
	Slippage        float64 `json:"slippage"`
	InfiniteApprove bool    `json:"infiniteApprove"`
}

type UserSymbolPreferenceRequest struct {
	Symbol string `json:"symbol"`
}

type UserSymbolPreferenceResponse struct {
	IsFavorite      bool   `json:"isFavorite"`
	Leverage        int    `json:"leverage"`
	IsCross         bool   `json:"isCross"`
	TpslUnit        string `json:"tpslUnit"`
	OrderUnitInBase bool   `json:"orderUnitInBase"`
}

type UserTradeHistoryRequest struct {
	WalletAddress string `json:"walletAddress"`
}

type UserTradeHistoryResponse struct {
	Histories []*TradeHistory `json:"histories"`
}

type ValidationsRequest struct {
	Balance bool `json:"balance"`
	Fee     bool `json:"fee"`
	Approve bool `json:"approve"`
}

type ChainType string

const (
	ChainTypeEvm     ChainType = "EVM"
	ChainTypeSolana  ChainType = "SOLANA"
	ChainTypeTron    ChainType = "TRON"
	ChainTypeArb     ChainType = "ARB"
	ChainTypeBsc     ChainType = "BSC"
	ChainTypePolygon ChainType = "POLYGON"
)

var AllChainType = []ChainType{
	ChainTypeEvm,
	ChainTypeSolana,
	ChainTypeTron,
	ChainTypeArb,
	ChainTypeBsc,
	ChainTypePolygon,
}

func (e ChainType) IsValid() bool {
	switch e {
	case ChainTypeEvm, ChainTypeSolana, ChainTypeTron, ChainTypeArb, ChainTypeBsc, ChainTypePolygon:
		return true
	}
	return false
}

func (e ChainType) String() string {
	return string(e)
}

func (e *ChainType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = ChainType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid ChainType", str)
	}
	return nil
}

func (e ChainType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type Grouping string

const (
	GroupingNa           Grouping = "na"
	GroupingNormalTpsl   Grouping = "normalTpsl"
	GroupingPositionTpsl Grouping = "positionTpsl"
)

var AllGrouping = []Grouping{
	GroupingNa,
	GroupingNormalTpsl,
	GroupingPositionTpsl,
}

func (e Grouping) IsValid() bool {
	switch e {
	case GroupingNa, GroupingNormalTpsl, GroupingPositionTpsl:
		return true
	}
	return false
}

func (e Grouping) String() string {
	return string(e)
}

func (e *Grouping) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = Grouping(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid Grouping", str)
	}
	return nil
}

func (e Grouping) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type NotificationTypeEnum string

const (
	NotificationTypeEnumPriceRise          NotificationTypeEnum = "priceRise"
	NotificationTypeEnumPriceFell          NotificationTypeEnum = "priceFell"
	NotificationTypeEnumPercent24hIncrease NotificationTypeEnum = "percent24hIncrease"
	NotificationTypeEnumPercent24hDecline  NotificationTypeEnum = "percent24hDecline"
)

var AllNotificationTypeEnum = []NotificationTypeEnum{
	NotificationTypeEnumPriceRise,
	NotificationTypeEnumPriceFell,
	NotificationTypeEnumPercent24hIncrease,
	NotificationTypeEnumPercent24hDecline,
}

func (e NotificationTypeEnum) IsValid() bool {
	switch e {
	case NotificationTypeEnumPriceRise, NotificationTypeEnumPriceFell, NotificationTypeEnumPercent24hIncrease, NotificationTypeEnumPercent24hDecline:
		return true
	}
	return false
}

func (e NotificationTypeEnum) String() string {
	return string(e)
}

func (e *NotificationTypeEnum) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = NotificationTypeEnum(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid NotificationTypeEnum", str)
	}
	return nil
}

func (e NotificationTypeEnum) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type OHLCIntervalEnum string

const (
	OHLCIntervalEnumOneDay         OHLCIntervalEnum = "ONE_DAY"
	OHLCIntervalEnumOneHour        OHLCIntervalEnum = "ONE_HOUR"
	OHLCIntervalEnumOneMonth       OHLCIntervalEnum = "ONE_MONTH"
	OHLCIntervalEnumOneMinute      OHLCIntervalEnum = "ONE_MINUTE"
	OHLCIntervalEnumOneWeek        OHLCIntervalEnum = "ONE_WEEK"
	OHLCIntervalEnumTwoHours       OHLCIntervalEnum = "TWO_HOURS"
	OHLCIntervalEnumThreeMinutes   OHLCIntervalEnum = "THREE_MINUTES"
	OHLCIntervalEnumThreeDays      OHLCIntervalEnum = "THREE_DAYS"
	OHLCIntervalEnumThreeMonths    OHLCIntervalEnum = "THREE_MONTHS"
	OHLCIntervalEnumFourHours      OHLCIntervalEnum = "FOUR_HOURS"
	OHLCIntervalEnumFiveMinutes    OHLCIntervalEnum = "FIVE_MINUTES"
	OHLCIntervalEnumEightHours     OHLCIntervalEnum = "EIGHT_HOURS"
	OHLCIntervalEnumTwelveHours    OHLCIntervalEnum = "TWELVE_HOURS"
	OHLCIntervalEnumFifteenMinutes OHLCIntervalEnum = "FIFTEEN_MINUTES"
	OHLCIntervalEnumThirtyMinutes  OHLCIntervalEnum = "THIRTY_MINUTES"
)

var AllOHLCIntervalEnum = []OHLCIntervalEnum{
	OHLCIntervalEnumOneDay,
	OHLCIntervalEnumOneHour,
	OHLCIntervalEnumOneMonth,
	OHLCIntervalEnumOneMinute,
	OHLCIntervalEnumOneWeek,
	OHLCIntervalEnumTwoHours,
	OHLCIntervalEnumThreeMinutes,
	OHLCIntervalEnumThreeDays,
	OHLCIntervalEnumThreeMonths,
	OHLCIntervalEnumFourHours,
	OHLCIntervalEnumFiveMinutes,
	OHLCIntervalEnumEightHours,
	OHLCIntervalEnumTwelveHours,
	OHLCIntervalEnumFifteenMinutes,
	OHLCIntervalEnumThirtyMinutes,
}

func (e OHLCIntervalEnum) IsValid() bool {
	switch e {
	case OHLCIntervalEnumOneDay, OHLCIntervalEnumOneHour, OHLCIntervalEnumOneMonth, OHLCIntervalEnumOneMinute, OHLCIntervalEnumOneWeek, OHLCIntervalEnumTwoHours, OHLCIntervalEnumThreeMinutes, OHLCIntervalEnumThreeDays, OHLCIntervalEnumThreeMonths, OHLCIntervalEnumFourHours, OHLCIntervalEnumFiveMinutes, OHLCIntervalEnumEightHours, OHLCIntervalEnumTwelveHours, OHLCIntervalEnumFifteenMinutes, OHLCIntervalEnumThirtyMinutes:
		return true
	}
	return false
}

func (e OHLCIntervalEnum) String() string {
	return string(e)
}

func (e *OHLCIntervalEnum) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = OHLCIntervalEnum(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid OHLCIntervalEnum", str)
	}
	return nil
}

func (e OHLCIntervalEnum) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type Operation string

const (
	OperationOpenOrder   Operation = "openOrder"
	OperationCancelOrder Operation = "cancelOrder"
)

var AllOperation = []Operation{
	OperationOpenOrder,
	OperationCancelOrder,
}

func (e Operation) IsValid() bool {
	switch e {
	case OperationOpenOrder, OperationCancelOrder:
		return true
	}
	return false
}

func (e Operation) String() string {
	return string(e)
}

func (e *Operation) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = Operation(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid Operation", str)
	}
	return nil
}

func (e Operation) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type OrderStatus string

const (
	OrderStatusFilled  OrderStatus = "filled"
	OrderStatusResting OrderStatus = "resting"
	OrderStatusCancel  OrderStatus = "cancel"
)

var AllOrderStatus = []OrderStatus{
	OrderStatusFilled,
	OrderStatusResting,
	OrderStatusCancel,
}

func (e OrderStatus) IsValid() bool {
	switch e {
	case OrderStatusFilled, OrderStatusResting, OrderStatusCancel:
		return true
	}
	return false
}

func (e OrderStatus) String() string {
	return string(e)
}

func (e *OrderStatus) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = OrderStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid OrderStatus", str)
	}
	return nil
}

func (e OrderStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type OrderType string

const (
	OrderTypeLimit    OrderType = "limit"
	OrderTypeMarket   OrderType = "market"
	OrderTypeTpMarket OrderType = "tp_market"
	OrderTypeSlMarket OrderType = "sl_market"
)

var AllOrderType = []OrderType{
	OrderTypeLimit,
	OrderTypeMarket,
	OrderTypeTpMarket,
	OrderTypeSlMarket,
}

func (e OrderType) IsValid() bool {
	switch e {
	case OrderTypeLimit, OrderTypeMarket, OrderTypeTpMarket, OrderTypeSlMarket:
		return true
	}
	return false
}

func (e OrderType) String() string {
	return string(e)
}

func (e *OrderType) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = OrderType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid OrderType", str)
	}
	return nil
}

func (e OrderType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type Side string

const (
	SideBuy  Side = "buy"
	SideSell Side = "sell"
)

var AllSide = []Side{
	SideBuy,
	SideSell,
}

func (e Side) IsValid() bool {
	switch e {
	case SideBuy, SideSell:
		return true
	}
	return false
}

func (e Side) String() string {
	return string(e)
}

func (e *Side) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = Side(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid Side", str)
	}
	return nil
}

func (e Side) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type SymbolConditionEnum string

const (
	SymbolConditionEnumVolume       SymbolConditionEnum = "volume"
	SymbolConditionEnumGainer       SymbolConditionEnum = "gainer"
	SymbolConditionEnumLoser        SymbolConditionEnum = "loser"
	SymbolConditionEnumTrend        SymbolConditionEnum = "trend"
	SymbolConditionEnumMarketCap    SymbolConditionEnum = "marketCap"
	SymbolConditionEnumOpenInterest SymbolConditionEnum = "openInterest"
	SymbolConditionEnumCategory     SymbolConditionEnum = "category"
)

var AllSymbolConditionEnum = []SymbolConditionEnum{
	SymbolConditionEnumVolume,
	SymbolConditionEnumGainer,
	SymbolConditionEnumLoser,
	SymbolConditionEnumTrend,
	SymbolConditionEnumMarketCap,
	SymbolConditionEnumOpenInterest,
	SymbolConditionEnumCategory,
}

func (e SymbolConditionEnum) IsValid() bool {
	switch e {
	case SymbolConditionEnumVolume, SymbolConditionEnumGainer, SymbolConditionEnumLoser, SymbolConditionEnumTrend, SymbolConditionEnumMarketCap, SymbolConditionEnumOpenInterest, SymbolConditionEnumCategory:
		return true
	}
	return false
}

func (e SymbolConditionEnum) String() string {
	return string(e)
}

func (e *SymbolConditionEnum) UnmarshalGQL(v any) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = SymbolConditionEnum(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid SymbolConditionEnum", str)
	}
	return nil
}

func (e SymbolConditionEnum) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}
