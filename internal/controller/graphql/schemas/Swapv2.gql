type Chain {
    chainId: String!
    chainImage: String!
    chainName: String!
    tokens: [TokenV2!]!
}

type TokenV2 {
    address: String!
    symbol: String!
    name: String!
    image: String!
    decimals: Int!
    usdPrice: Float!
    relayExtra: RelayTokenExtra
    rangoExtra: RangoTokenExtra
}

type RelayTokenExtra {
    id: String!
}

type RangoTokenExtra {
    id: String!
}

type ExchangeMetaResponse {
      chains: [Chain!]!
}

input QuoteRequest {
  user: String!
  originId: String!
  destinationId: String!
  recipient: String!
  amount: String!
  type: String!
  requestId : String
}


type QuoteResponse {
    items: [StepItem!]!
    requestId: String!
    type       : String!
    description : String!
    errorCode    : String!
    outPutAmountFormatted : String!
    gasTopupAmount : String!
    gasTopupAmountFormatted : String!
    gasTopupAmountUsd : String!
    gasAmountFormatted : String!
    platformFeeAmountFormat : String!
    platformFeeSymbol : String!
}

type StepItem {
    from: String!
    to: String!
    data: String!
    value: String!
    valueAmountUsd      :String!
    valueAmountFormatted : String!
    chainId: String!
    gas: String!
    gasAmountUsd: String!
    gasAmountFormatted: String!
    maxFeePerGas: String!
    maxPriorityFeePerGas: String!
    instructions: [Instruction!]
    serializedMessage: [Int!]
    nonce : String!
    blockHash: String!
    transactionType    :String!
    estimatedTimeInSeconds: Int!
    isApprovalTx     :Boolean!
}

type Instruction {
    keys: [InstructionKey!]!
    programId: String!
    data: String!
}

type InstructionKey {
    pubkey: String!
    isSigner: Boolean!
    isWritable: Boolean!
}




type History {
    id: ID!
    requestId: String!
    userAddress: String!
    fromBlockchain: String!
    fromSymbol: String!
    fromAddress: String!
    toBlockchain: String!
    toSymbol: String!
    toAddress: String!
    requestAmount: String!
    outputAmount: String!
    resultType: String!
    validationStatus: String!
    walletNotSupportingFromBlockchain: Boolean!
    missingBlockchains: String
    diagnosisMessages: String
    status: String!
    step: Int!
    failReason: String
    swaps: [RangoSwaps!]
}

input HistoryRequest {
    address: String!
    blockchain: String!
    page: Int!
    pageSize: Int!
    startTime: Int
    endTime: Int
    status: String
}

type HistoryResponse {
    total: Int!
    list: [History!]!
}
