# NodeTrade GraphQL Schema

input UserNodeTradesRequest {
  lastID: String!
  limit: Int!
}


type RespGetUserNodeTrades {
  orders: [NodeTrades!]!
}

type NodeTrades {
  coin: String!
  side: String!
  time: String!
  px: String!
  sz: String!
  hash: String!
  tradeDirOverride: String!
  sideInfoTrade: [SideInfoTrade!]!
}

type SideInfoTrade {
  user: String!
  startPos: String!
  oid: Int
  twapId: Int
  cloid: String
}


