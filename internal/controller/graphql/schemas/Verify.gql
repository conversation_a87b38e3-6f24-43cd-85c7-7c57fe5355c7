input GenerateCloidInput {
  count: Int
}

type GenerateCloidResponse {
  cloids: [String!]!
  count: Int!
}

input LogTransactionInput {
  oid: String
  baseCoin: String
  side: Side
  price: String
  size: String
  orderType: OrderType
  operation: Operation
  orderStatus: OrderStatus
  cloid: String
  avgPx: String
  totalSz: String
  grouping: Grouping
  created: String
  walletAddress: String
}

enum Side {
  buy
  sell
}

enum OrderType {
  limit
  market
  tp_market
  sl_market
}

enum Operation {
  openOrder
  cancelOrder
}

enum OrderStatus {
  filled
  resting
  cancel
}

enum Grouping {
  na
  normalTpsl
  positionTpsl
}

type LogTransactionResponse {
  error: String
  status: String!
}
