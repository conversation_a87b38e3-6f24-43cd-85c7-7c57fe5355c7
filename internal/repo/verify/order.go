package verify

import (
	"time"

	"github.com/google/uuid"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/request"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
)

type VerifyOrderRepository struct{}

func (r *VerifyOrderRepository) CreateOrder(order *model.HyperLiquidVerifyOrder) error {
	return global.GVA_DB.Create(order).Error
}

func (r *VerifyOrderRepository) GetOrderByCloid(cloid string) (*model.HyperLiquidVerifyOrder, error) {
	var order model.HyperLiquidVerifyOrder
	err := global.GVA_DB.Where("cloid = ?", cloid).First(&order).Error
	if err != nil {
		return nil, err
	}
	return &order, nil
}

func (r *VerifyOrderRepository) GetOrderByOid(oid string) (*model.HyperLiquidVerifyOrder, error) {
	var order model.HyperLiquidVerifyOrder
	err := global.GVA_DB.Where("oid = ?", oid).First(&order).Error
	if err != nil {
		return nil, err
	}
	return &order, nil
}

func (r *VerifyOrderRepository) UpdateOrder(order *model.HyperLiquidVerifyOrder) error {
	return global.GVA_DB.Save(order).Error
}

func (r *VerifyOrderRepository) UpdateTransactionByOid(order *model.HyperLiquidVerifyOrder) error {
	return global.GVA_DB.Model(&model.HyperLiquidVerifyOrder{}).
		Where("oid = ?", order.OID).Updates(order).Error
}

func (r *VerifyOrderRepository) UpdateOrderInformationByOid(order *model.HyperLiquidVerifyOrder) error {
	return global.GVA_DB.Model(&model.HyperLiquidVerifyOrder{}).
		Where("oid = ?", order.OID).Updates(order).Error
}

func (r *VerifyOrderRepository) UpdateTransactionByCloid(order *model.HyperLiquidVerifyOrder) error {
	return global.GVA_DB.Model(&model.HyperLiquidVerifyOrder{}).
		Where("cloid = ?", order.Cloid).Updates(order).Error
}

func (r *VerifyOrderRepository) UpdateOrderInformationByCloid(order *model.HyperLiquidVerifyOrder) error {
	return global.GVA_DB.Model(&model.HyperLiquidVerifyOrder{}).
		Where("cloid = ?", order.Cloid).Updates(order).Error
}

func (r *VerifyOrderRepository) GetUnverifiedOrder() ([]*model.HyperLiquidVerifyOrder, error) {
	var orders []*model.HyperLiquidVerifyOrder
	err := global.GVA_DB.Where("is_verified = ?", false).Find(&orders).Error
	return orders, err
}

func (r *VerifyOrderRepository) GetOrdersForCSVVerification(csvData string) ([]*model.HyperLiquidVerifyOrder, error) {
	var orders []*model.HyperLiquidVerifyOrder
	startTime, err := time.Parse("2006-01-02", csvData)
	if err != nil {
		return nil, err
	}
	endTime := startTime.Add(24 * time.Hour)

	err = global.GVA_DB.
		Where("created_at >= ? AND created_at < ?", startTime, endTime).
		Where("csv_verified = ?", false).
		Find(&orders).Error
	return orders, err
}

func (r *VerifyOrderRepository) ExistsByCloid(cloid string) (bool, error) {
	var count int64
	err := global.GVA_DB.Model(&model.HyperLiquidVerifyOrder{}).
		Where("cloid = ?", cloid).
		Count(&count).Error
	return count > 0, err
}

func (r *VerifyOrderRepository) GetOrderByTokenAndCriteria(req request.ReqTransaction, userID uuid.UUID) (*model.HyperLiquidVerifyOrder, error) {
	query := global.GVA_DB.Model(&model.HyperLiquidVerifyOrder{}).
		Where("user_id = ?", userID)

	if req.Oid != "" {
		query = query.Where("oid = ?", req.Oid)
	}

	if req.BaseCoin != "" {
		query = query.Where("coin = ?", req.BaseCoin)
	}

	if req.Price != "" {
		query = query.Where("price = ?", req.Price)
	}

	if req.Size != "" {
		query = query.Where("size = ?", req.Size)
	}

	if req.AvgPx != "" {
		query = query.Where("avg_price = ?", req.AvgPx)
	}

	if req.TotalSz != "" {
		query = query.Where("total_sz = ?", req.TotalSz)
	}

	var order model.HyperLiquidVerifyOrder
	err := query.First(&order).Error
	if err != nil {
		return nil, err
	}
	return &order, nil
}

func (r *VerifyOrderRepository) UpdateTransactionByToken(order *model.HyperLiquidVerifyOrder) error {
	return global.GVA_DB.Debug().Model(&model.HyperLiquidVerifyOrder{}).Where("id = ?", order.ID).Updates(order).Error
}

func (r *VerifyOrderRepository) CreateOrders(orders []*model.HyperLiquidVerifyOrder) error {
	return global.GVA_DB.Create(orders).Error
}
