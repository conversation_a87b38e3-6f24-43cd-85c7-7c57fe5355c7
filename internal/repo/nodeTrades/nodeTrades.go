package nodetrades

import (
	"github.com/google/uuid"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo"
)

var NodeTrades repo.NodeTradesRepo = new(nodeTrades)

type nodeTrades struct {
}

func (s *nodeTrades) GetUserNodeTrades(userID uuid.UUID, lastID uuid.UUID, limit int) ([]*model.UserNodeTrades, error) {

	return nil, nil
}

func (s *nodeTrades) GetSideInfoByCloid(cloid string) (*model.UserNodeTrades, error) {
	var info model.UserNodeTradesSideInfo
	err := global.GVA_DB.Where("cloid = ?", cloid).First(&info).Error
	if err != nil {
		return nil, err
	}
	var trade model.UserNodeTrades
	err = global.GVA_DB.Where("id = ?", info.TradeID).First(&trade).Error
	if err != nil {
		return nil, err
	}
	return &trade, nil
}
