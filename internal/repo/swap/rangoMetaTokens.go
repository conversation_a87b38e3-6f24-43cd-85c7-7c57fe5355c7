package swap

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/config"
	"gorm.io/gorm"

	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/cache"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/constant"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
)

func (s *swap) Tokens() ([]*model.RangoMetaTokens, error) {
	var entities []*model.RangoMetaTokens
	//dbTx := global.GVA_DB.Where("block_chain IN ? AND address IN ? AND is_active = true", supportChains, supportTokens).//todo zw adjust this later
	dbTx := global.GVA_DB.Where("block_chain IN ? AND address IN ?", supportChains, supportTokens).
		Order("is_popular DESC, created_at DESC").
		Find(&entities)
	err := model.HandleDBTxError(dbTx)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return entities, nil
}

func (s *swap) GetSupportTokens() ([]*model.RangoMetaTokens, error) {

	key := cache.KeyRangoMetaTokens()
	var entities []*model.RangoMetaTokens
	ctx := context.Background()
	str, err := global.GVA_REDIS.Get(ctx, key).Result()
	if err == nil && str != "" {
		if err := json.Unmarshal([]byte(str), &entities); err == nil {
			return entities, nil
		}
	}
	entities, err = s.Tokens()
	if err != nil {
		return nil, err
	}
	data, err := json.Marshal(entities)
	if err == nil {
		_ = global.GVA_REDIS.Set(ctx, key, data, constant.TenMinuteCacheTime).Err()
	}

	return entities, nil
}
func (s *swap) GetToken(chain, symbol, address string) (*model.RangoMetaTokens, error) {

	tokens, err := s.GetSupportTokens()
	if err != nil {
		return nil, fmt.Errorf("[Repo]GetToken : %w", err)
	}
	for _, token := range tokens {
		if token.BlockChain == chain &&
			token.Symbol == symbol &&
			token.Address == address {
			return token, nil
		}
	}
	return nil, nil
}

func (s *swap) GetRangoTokenByUUID(id string) (*model.RangoMetaTokens, error) {

	key := cache.KeyRangoTokenByUUID(id)
	var entity model.RangoMetaTokens
	ctx := context.Background()
	str, err := global.GVA_REDIS.Get(ctx, key).Result()
	if err == nil && str != "" {
		if err := json.Unmarshal([]byte(str), &entity); err == nil {
			return &entity, nil
		}
	}

	dbTx := global.GVA_DB.Model(&model.RangoMetaTokens{}).Where("id = ?", id).First(&entity)
	err = model.HandleDBTxError(dbTx)
	if err != nil {
		return nil, err
	}

	data, err := json.Marshal(&entity)
	if err != nil {
		return nil, err
	}

	str, err = global.GVA_REDIS.Set(ctx, key, data, constant.OneHourCacheTime).Result()
	if err == nil && str != "" {
		if err := json.Unmarshal([]byte(str), &entity); err == nil {
			return &entity, nil
		}
	}

	return &entity, nil
}

func (s *swap) RangoMetaData() (*config.MetaData, error) {
	var (
		metaChains       []*config.ChainMeta
		metaChainsFilter = make(map[string]*config.ChainMeta)
	)

	tokens, err := s.GetSupportTokens()
	if err != nil {
		return nil, err
	}
	var tokenFilter = make(map[string][]*config.TokenMeta)
	for _, token := range tokens {
		var metaToken config.TokenMeta
		metaToken.Assign(token.ID.String(), token.Address, token.Symbol, token.Name, token.Image,
			int(token.Decimals), token.UsdPrice, token.IsNative, map[string]string{constant.Rango: token.ID.String()})
		tokenFilter[token.BlockChain] = append(tokenFilter[token.BlockChain], &metaToken)
	}

	chains, err := s.GetRangoChains()
	if err != nil {
		return nil, err
	}
	for _, chain := range chains {
		var metaChain config.ChainMeta
		metaChain.Assign(chain.ChainId, chain.Logo, chain.Name)
		if ok := tokenFilter[metaChain.ChainName] != nil; !ok {
			return nil, fmt.Errorf("[rango repo]no tokens found for chain: %s", metaChain.ChainName)
		}
		metaChain.AssignTokens(tokenFilter[metaChain.ChainName])
		metaChainsFilter[metaChain.Key()] = &metaChain
		metaChains = append(metaChains, &metaChain)
	}
	//special handling for chains that are not EVM chains
	for i, chain := range metaChains {
		if v, ok := constant.TurnKeyNoEvmChainId[chain.ChainId]; ok {
			delete(metaChainsFilter, metaChains[i].ChainId)

			metaChains[i].ChainId = v
			metaChainsFilter[metaChains[i].Key()] = metaChains[i]
		}
	}
	return &config.MetaData{
		Chains:      metaChains,
		ChainFilter: metaChainsFilter,
	}, nil
}
