package swap_test

import (
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/app"
	swap2 "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/swap"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/task/swap"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/rango"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/relay"
	"testing"
)

func TestRelySave(t *testing.T) {
	var s = app.GraphqlServer{}
	s.Initialize()

	chains, err := relay.GetMetaData()
	if err != nil {
		t.Fatalf("%v", err)
	}
	chainsModel, tokensModel, err := swap2.RelayChainsAssign(chains)
	if err != nil {
		t.Fatalf("failed to assign chains: %v", err)
	}
	swap := swap2.Swap
	err = swap.SaveRelayMetaData(chainsModel, tokensModel)
	if err != nil {
		t.Fatalf("failed to save relay metadata: %v", err)
	}
}

func TestRangoSave(t *testing.T) {
	var s = app.GraphqlServer{}
	s.Initialize()
	result, err := rango.GetExchangeMeta()
	if err != nil {
		t.Fatalf("ResetRangoMetaData get err:%v", err)
	}
	if len(result.Tokens) == 0 {
		t.Fatalf("ResetRangoMetaData err: token is empty")
	}
	if len(result.PopularTokens) == 0 {
		t.Fatalf("ResetRangoMetaData err: token is empty")
	}
	if len(result.Blockchains) == 0 {
		t.Fatalf("ResetRangoMetaData err: blockchain is empty")
	}
	if len(result.Swappers) == 0 {
		t.Fatalf("ResetRangoMetaData err: swap is empty")
	}
	chains, err := swap2.ChainsAssign(result.Blockchains)
	if err != nil {
		t.Fatalf("QueryRango Tokens err:%v", err)
	}
	tokens, err := swap2.TokenAssign(result.Tokens)
	if err != nil {
		t.Fatalf("QueryRango Tokens err:%v", err)
	}
	swappers, err := swap2.SwappersAssign(result.Swappers)
	if err != nil {
		t.Fatalf("QueryRangoMeta Swappers err:%v", err)
	}
	swap := swap2.Swap
	err = swap.SaveRangoMetaData(chains, tokens, swappers)
	if err != nil {
		t.Fatalf("ResetRangoMetaData save err:%v", err)
	}
}
func TestRelayCapacitySave(t *testing.T) {
	var s = app.GraphqlServer{}
	s.Initialize()
	swapTask := swap.NewSwapTask(swap2.Swap)
	swapTask.SyncRelayCapacity()()
}
