package swap

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/redis/go-redis/v9"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/config"
	"reflect"

	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/cache"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/constant"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func (s *swap) GetRelayChains() ([]*model.RelayMetaChains, error) {
	var dbEntities []*model.RelayMetaChains
	key := cache.KeyRelayMetaChains()
	ctx := context.Background()
	str, err := global.GVA_REDIS.Get(ctx, key).Result()
	if err == nil && str != "" {
		if err := json.Unmarshal([]byte(str), &dbEntities); err == nil {
			return dbEntities, nil
		}
	}
	dbEntities, err = s.RelayMetaChains()
	if err != nil {
		return nil, err
	}

	data, err := json.Marshal(dbEntities)
	if err == nil {
		_ = global.GVA_REDIS.Set(ctx, key, data, constant.TenMinuteCacheTime).Err()
	}

	return dbEntities, nil
}
func (s *swap) GetRelayTokens() ([]*model.RelayMetaTokens, error) {
	var dbEntities []*model.RelayMetaTokens
	key := cache.KeyRelayMetaTokens()
	ctx := context.Background()
	str, err := global.GVA_REDIS.Get(ctx, key).Result()
	if err == nil && str != "" {
		if err := json.Unmarshal([]byte(str), &dbEntities); err == nil {
			return dbEntities, nil
		}
	}
	dbEntities, err = s.RelayMetaTokens()
	if err != nil {
		return nil, err
	}

	data, err := json.Marshal(dbEntities)
	if err == nil {
		_ = global.GVA_REDIS.Set(ctx, key, data, constant.TenMinuteCacheTime).Err()
	}

	return dbEntities, nil
}

func (s *swap) GetRelayTokenByUUID(id string) (*model.RelayMetaTokens, error) {
	key := cache.KeyRelayTokenByUUID(id)
	var token model.RelayMetaTokens
	ctx := context.Background()
	str, err := global.GVA_REDIS.Get(ctx, key).Result()
	if err == nil && str != "" {
		if err := json.Unmarshal([]byte(str), &token); err == nil {
			return &token, nil
		}
	}

	dbTx := global.GVA_DB.Model(&model.RelayMetaTokens{}).Where("id = ?", id).First(&token)
	err = model.HandleDBTxError(dbTx)
	if err != nil {
		return nil, err
	}

	data, err := json.Marshal(&token)
	if err != nil {
		return nil, err
	}

	str, err = global.GVA_REDIS.Set(ctx, key, data, constant.OneHourCacheTime).Result()
	if err == nil && str != "" {
		if err := json.Unmarshal([]byte(str), &token); err == nil {
			return &token, nil
		}
	}

	return &token, nil
}

func (s *swap) GetRelayChainById(id string) (*model.RelayMetaChains, error) {

	key := cache.KeyRelayChainById(id)
	var entity model.RelayMetaChains
	ctx := context.Background()
	str, err := global.GVA_REDIS.Get(ctx, key).Result()
	if err == nil && str != "" {
		if err := json.Unmarshal([]byte(str), &entity); err == nil {
			return &entity, nil
		}
	}

	dbTx := global.GVA_DB.Model(&model.RelayMetaChains{}).Where("chain_id = ?", id).First(&entity)
	err = model.HandleDBTxError(dbTx)
	if err != nil {
		return nil, err
	}

	data, err := json.Marshal(&entity)
	if err != nil {
		return nil, err
	}

	str, err = global.GVA_REDIS.Set(ctx, key, data, constant.OneHourCacheTime).Result()
	if err == nil && str != "" {
		if err := json.Unmarshal([]byte(str), &entity); err == nil {
			return &entity, nil
		}
	}

	return &entity, nil
}

func (s *swap) RelayMetaChains() ([]*model.RelayMetaChains, error) {
	var entities []*model.RelayMetaChains
	dbTx := global.GVA_DB.Select("*").
		Where("chain_id_hex IN ? ", supportChainIds).
		Order("created_at asc").
		Find(&entities)
	err := model.HandleDBTxError(dbTx)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	return entities, nil
}
func (s *swap) RelayMetaTokens() ([]*model.RelayMetaTokens, error) {
	var entities []*model.RelayMetaTokens
	dbTx := global.GVA_DB.Select("*").
		Where("chain_id_hex IN ? AND address IN ?", supportChainIds, supportTokens).
		Order("created_at asc").
		Find(&entities)
	err := model.HandleDBTxError(dbTx)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	return entities, nil
}

func (s *swap) SaveRelayMetaData(
	chains []*model.RelayMetaChains,
	tokens []*model.RelayMetaTokens,
) error {
	tx := global.GVA_DB

	save := func(updateField []string, data any, conflictColumns []clause.Column) error {
		if reflect.ValueOf(data).Len() == 0 {
			return nil
		}
		dbTx := tx.Debug().Clauses(clause.OnConflict{
			Columns:   conflictColumns,
			DoUpdates: clause.AssignmentColumns(updateField),
		}).CreateInBatches(data, constant.MidCreateSize)
		return model.HandleDBTxError(dbTx)
	}

	var g errgroup.Group
	g.Go(func() error {
		return save([]string{
			"chain_id",
			"chain_id_hex",
			"name",
			"display_name",
			"http_rpc_url",
			"ws_rpc_url",
			"explorer_url",
			"explorer_name",
			"deposit_enabled",
			"token_support",
			"disabled",
			"partial_disable_limit",
			"block_production_lagging",
			"withdrawal_fee",
			"deposit_fee",
			"surge_enabled",
			"icon_url",
			"contracts",
			"vm_type",
			"base_chain_id",
			"solver_addresses",
			"tags",
			"logo_url",
			"brand_color",
			"explorer_paths",
		}, chains, []clause.Column{{Name: "chain_id_hex"}})

	})

	g.Go(func() error {

		for i := 0; i < len(tokens); i += constant.MidCreateSize {
			end := i + constant.MidCreateSize
			if end > len(tokens) {
				end = len(tokens)
			}
			chunk := tokens[i:end]
			err := save([]string{
				"chain_id",
				"chain_id_hex",
				"token_id",
				"symbol",
				"name",
				"address",
				"decimals",
				"supports_bridging",
				"withdrawal_fee",
				"deposit_fee",
				"surge_enabled",
				"supports_permit",
				"usd_price",
				"logo_uri",
			},
				chunk, []clause.Column{
					{Name: "token_id"}, {Name: "chain_id_hex"}, {Name: "address"}},
			)
			if err != nil {
				return fmt.Errorf("batch %d-%d failed: %w", i, end, err)
			}
		}

		return nil
	})

	return g.Wait()
}

func (s *swap) RelayMetaData() (*config.MetaData, error) {
	var (
		metaChains       []*config.ChainMeta
		metaChainsFilter = make(map[string]*config.ChainMeta)
	)

	tokens, err := s.GetRelayTokens()
	if err != nil {
		return nil, err
	}
	var tokenTempFilter = make(map[string][]*config.TokenMeta)
	for _, token := range tokens {
		var metaToken config.TokenMeta
		metaToken.Assign(token.ID.String(), token.Address, token.Symbol, token.Name, token.LogoURI, token.Decimals,
			0, token.IsNative, map[string]string{constant.Relay: token.ID.String()})
		tokenTempFilter[token.ChainId] = append(tokenTempFilter[token.ChainId], &metaToken)
	}

	chains, err := s.GetRelayChains()
	if err != nil {
		return nil, err
	}
	for _, chain := range chains {
		var metaChain config.ChainMeta
		metaChain.Assign(chain.ChainId, chain.IconUrl, chain.Name)
		if ok := tokenTempFilter[metaChain.ChainId] != nil; !ok {
			return nil, fmt.Errorf("[relay repo]no tokens found for chain: %s", metaChain.ChainName)
		}
		metaChain.AssignTokens(tokenTempFilter[metaChain.ChainId])
		metaChainsFilter[metaChain.Key()] = &metaChain
		metaChains = append(metaChains, &metaChain)
	}
	//special handling for chains that are not EVM chains
	for i, chain := range metaChains {
		if v, ok := constant.TurnKeyNoEvmChainId[chain.ChainId]; ok {
			delete(metaChainsFilter, metaChains[i].ChainId)

			metaChains[i].ChainId = v
			metaChainsFilter[metaChains[i].Key()] = metaChains[i]
		}
	}
	return &config.MetaData{
		Chains:      metaChains,
		ChainFilter: metaChainsFilter,
	}, nil
}

func (s *swap) ReloadRelay2RangoToken(relayId string) (string, error) {
	str, err := global.GVA_REDIS.Get(context.Background(), cache.KeyRelay2Rango(relayId)).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		return str, fmt.Errorf("failed to get relay2rango token: %w", err)
	}
	if errors.Is(err, redis.Nil) {
		return relayId, nil
	}
	return str, nil
}
func (s *swap) StoreRelay2RangoToken(relayId, rangoId string) error {
	str, err := global.GVA_REDIS.Get(context.Background(), cache.KeyRelay2Rango(relayId)).Result()
	if str != "" {
		return nil
	}
	err = global.GVA_REDIS.Set(context.Background(), cache.KeyRelay2Rango(relayId), rangoId,
		0).Err()
	if err != nil {
		return err
	}
	return nil
}

func (s *swap) GetRelayCapacity(chainId, tokenId string) (*model.RelayCapacity, error) {
	var capacity model.RelayCapacity
	str, err := global.GVA_REDIS.Get(context.Background(), cache.KeyRelayCapacity(chainId, tokenId)).Result()
	if str != "" {
		err = json.Unmarshal([]byte(str), &capacity)
		return &capacity, nil
	}
	dbTx := global.GVA_DB.
		Where("chain_id = ? AND token_id = ?", chainId, tokenId).
		Find(&capacity)
	err = model.HandleDBTxError(dbTx)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	bytes, err := json.Marshal(capacity)
	if err != nil {
		return nil, err
	}

	err = global.GVA_REDIS.Set(context.Background(), cache.KeyRelayCapacity(chainId, tokenId), string(bytes), constant.FiveMinuteCacheTime).Err()
	if err != nil {
		return nil, err
	}
	return &capacity, nil
}

func (s *swap) AllRelayCapacity() ([]*model.RelayCapacity, error) {
	var capacity []*model.RelayCapacity
	dbTx := global.GVA_DB.Model(model.RelayCapacity{}).Select("*").Scan(&capacity)
	if dbTx.Error != nil {
		return nil, dbTx.Error
	}
	return capacity, nil
}

func (s *swap) SaveRelayCapacity(data any) error {
	dbTx := global.GVA_DB.Model(&model.RelayCapacity{}).Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: "chain_id"}, {Name: "token_id"}, {Name: "solver_address"}},
		DoUpdates: clause.AssignmentColumns([]string{
			"capacity_volume",
		}),
	}).CreateInBatches(data, constant.MidCreateSize)
	err := model.HandleDBTxError(dbTx)
	if err != nil {
		return nil
	}
	return nil
}
