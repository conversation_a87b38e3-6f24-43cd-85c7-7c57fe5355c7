package aggregate

import (
	"context"
	"encoding/json"
	"fmt"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/cache"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/config"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/constant"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo"
	repoSwap "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/swap"
)

var Aggregate repo.AggregateRepo = new(aggregate)

type aggregate struct {
}

func (a *aggregate) SwapMetaData() (*config.MetaData, error) {
	var metaData config.MetaData
	cacheKey := fmt.Sprintf("%s", cache.KeyRelayMetaData())
	str, err := global.GVA_REDIS.Get(context.Background(), cacheKey).Result()
	if err == nil && str != "" {
		if err = json.Unmarshal([]byte(str), &metaData); err != nil {
			return nil, fmt.Errorf("failed to unmarshal symbol detail data: %w", err)
		}
		return &metaData, nil
	}

	relayMeta, err := repoSwap.Swap.RelayMetaData()
	if err != nil {
		return nil, err
	}
	rangoMeta, err := repoSwap.Swap.RangoMetaData()
	if err != nil {
		return nil, err
	}
	for _, chain := range relayMeta.Chains {
		err = chain.CombineMetaData(rangoMeta.ChainFilter)
		if err != nil {
			return nil, err
		}
		for _, t := range chain.Tokens {
			relayId := t.Extra[constant.Relay]
			rangoId := t.Extra[constant.Rango]
			err := repoSwap.Swap.StoreRelay2RangoToken(relayId, rangoId)
			if err != nil {
				return nil, fmt.Errorf("failed to store relay2rango token: %w", err)
			}
		}
	}
	jsonData, err := json.Marshal(relayMeta)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal relay meta data: %w", err)
	}
	err = global.GVA_REDIS.Set(context.Background(), cacheKey, string(jsonData), 0).Err()
	if err != nil {
		return nil, fmt.Errorf("failed to cache relay meta data: %w", err)
	}
	return relayMeta, nil
}
