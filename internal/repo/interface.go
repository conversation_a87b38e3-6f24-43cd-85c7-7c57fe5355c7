// Package repo implements application outer layer logic. Each logic group in own file.
package repo

import (
	"time"

	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/config"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/dto/request"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/rango"
	"golang.org/x/net/context"
)

//go:generate mockgen -source=contracts.go -destination=../usecase/mocks_repo_test.go -package=usecase_test

type (
	// SwapRepo -.
	SwapRepo interface {
		//rango
		Histories(blockchain, address string) ([]*model.RangoHistory, int64, error)
		GetHistoryById(requestId string) (*model.RangoHistory, error)
		CreateOrUpdateHistory(repoEntity *model.RangoHistory) error
		GetRangoChains() ([]*model.RangoMetaChains, error)
		GetChainByName(chain string) (*model.Affiliate, error)
		GetSwappers() ([]*model.RangoMetaSwappers, error)
		Tokens() ([]*model.RangoMetaTokens, error)
		GetSupportTokens() ([]*model.RangoMetaTokens, error)
		GetToken(chain, symbol, address string) (*model.RangoMetaTokens, error)
		UpdateSwapStep(repoEntity *model.RangoHistory, swap *model.RangoSwaps) error
		SaveRangoMetaData(chains []*model.RangoMetaChains, tokens []*model.RangoMetaTokens, swappers []*model.RangoMetaSwappers) error
		CacheConfirm(data rango.ConfirmRouteResult) error
		LoadConfirmed(requestId string) (confirm rango.ConfirmRouteResult, err error)
		UpdateRangoChainActiveStatus() error
		UpdateRangoTokenActiveStatus() error
		GetRangoChainActiveStatus(chain string) (bool, error)
		GetRangoTokenActiveStatus(chain, symbol, address string) (bool, error)
		CacheStatus(requestId string, step int64, result string) error
		LoadStatus(requestId string, step int64) (string, error)

		//relay
		GetRelayChains() ([]*model.RelayMetaChains, error)
		GetRelayTokens() ([]*model.RelayMetaTokens, error)
		SaveRelayMetaData(
			chains []*model.RelayMetaChains,
			tokens []*model.RelayMetaTokens,
		) error
		GetRelayTokenByUUID(id string) (*model.RelayMetaTokens, error)
		GetRelayChainById(id string) (*model.RelayMetaChains, error)
		GetRangoTokenByUUID(id string) (*model.RangoMetaTokens, error)
		GetRangoChainByName(id string) (*model.RangoMetaChains, error)
		RangoMetaData() (*config.MetaData, error)
		RelayMetaData() (*config.MetaData, error)
		ReloadRelay2RangoToken(relayId string) (string, error)
		StoreRelay2RangoToken(relayId, rangoId string) error

		AllRelayCapacity() ([]*model.RelayCapacity, error)
		GetRelayCapacity(chainId, tokenId string) (*model.RelayCapacity, error)
		SaveRelayCapacity(data any) error
	}

	MarketRepo interface {
		// VaultPositionRepository methods
		GetVaultPositions(userID int64, symbol string, page, pageSize int) ([]model.VaultPosition, int64, error)
		GetVaultPositionByID(positionID int64) (*model.VaultPosition, error)
		CreateVaultPosition(position *model.VaultPosition) error
		UpdateVaultPosition(position *model.VaultPosition) error
		DeleteVaultPosition(positionID int64) error
		GetOpenPositions(userID int64) ([]model.VaultPosition, error)
		GetTotalUnrealizedPnl(userID int64) (float64, error)

		// TradeOrderRepository methods
		GetTradeOrders(accountID int64, symbol string, page, pageSize int) ([]model.TradeOrder, int64, error)
		GetTradeOrderByID(orderID int64) (*model.TradeOrder, error)
		CreateTradeOrder(order *model.TradeOrder) error
		UpdateTradeOrder(order *model.TradeOrder) error
		UpdateOrderStatus(orderID int64, status string) error

		// UserRepository methods
		GetUserByID(id int64) (*model.User, error)
		GetUserByEmail(email string) (*model.User, error)
		GetUserByUsername(username string) (*model.User, error)
		CreateUser(user *model.User) error
		UpdateUser(user *model.User) error
		UpdateUserFund(id int64, fund float64) error
	}

	SymbolRepo interface {
		GetOHLC(symbol, interval string, timestamp int64, isForward bool, limit int) ([]*model.OHLC, error)
		GetSymbolsData(whereCondition, orderClause string) ([]*model.CoinData, error)
		GetSymbolDataByCategory(categoryName string) ([]*model.CoinData, error)
		GetSymbolOneDayRange(symbol string) (float64, float64, error)
		GetSymbolOverallData(symbol string) (*model.Coin, error)
		GetSymbolStatistic(symbol string) (*model.CoinStatistic, error)
		UpsertFavoriteSymbol(userID uuid.UUID, symbol string, isFavorite bool) (*model.UserCoin, error)
		GetFavoriteSymbols(userID uuid.UUID) ([]*model.UserCoin, error)
		GetCategory() ([]string, error)
		GetUserSymbolDetail(userID uuid.UUID, symbol string) (*model.UserCoin, error)
		UpsertUserSymbolPreference(userID uuid.UUID, symbol string, upsertField map[string]interface{}) (*model.UserCoin, error)
		GetUserSymbolPreference(userID uuid.UUID, symbol string) (*model.UserCoin, error)
		UpdateSymbolData(symbolList []model.Coin, symbolStatistic []model.CoinStatistic) error
		UpdateTotalSymbolData(symbolStatistics []model.CoinStatistic) error
		UpdateTokenAddressData(symbolOverallData []model.Coin) error
		CacheSymbolData(data any, cacheKey string, cacheTime time.Duration) error
		GetSymbolList() ([]string, error)
		GetNewSymbolFromDB() ([]string, error)
		GetNewSymbolFromCache() ([]string, error)
		CacheNewSymbol(symbolList []string) error
		GetPopularSymbol(number int64) ([]redis.Z, error)
		UpdatePopularSymbol(symbol string) error
		GetAlertSymbolsSetting(userID uuid.UUID) ([]*model.UserNotificationSetting, error)
		UpdateAlertSymbolSetting(setting model.UserNotificationSetting) error
		MarkNotifiedSetting(setting model.UserNotificationSetting) error
		InsertAlertSymbolSetting(setting model.UserNotificationSetting) error
		GetActiveSettingsBySymbol(ctx context.Context, symbol string) ([]*model.UserNotificationSetting, error)
		DeleteAlertSymbolSetting(settingIDs []uuid.UUID) error
		GetSignal(user string, limit, offset int) ([]*model.SymbolSignal, int64, error)
	}

	TradeRepo interface {
		GetUserOpenOrder(userID uuid.UUID, lastID uuid.UUID, limit int) ([]*model.Order, error)
		GetUserPosition(walletAddress string) ([]*model.Position, error)
		UpdateUserPosition(position []*model.Position) error
		StoreTxInformation(order *model.Order) error
	}
	OrderStatusRepo interface {
		GetUserOrderStatus(userID uuid.UUID, lastID uuid.UUID, limit int) ([]*model.UserOrderStatus, error)
	}
	MiscEventRepo interface {
		GetUserMiscEvents(userID uuid.UUID, lastID uuid.UUID, limit int) ([]*model.UserMiscEvent, error)
	}
	NodeTradesRepo interface {
		GetUserNodeTrades(userID uuid.UUID, lastID uuid.UUID, limit int) ([]*model.UserNodeTrades, error)
		GetSideInfoByCloid(cloid string) (*model.UserNodeTrades, error)
	}
	AssetRepo interface {
		GetAllUserWallets(offset, limit int) ([]*model.UserWallet, error)
		GetUserWalletsByID(userID uuid.UUID) ([]string, error)
		SaveUserBalance(balance model.UserBalance) error
		GetUserBalance(wallet string) (model.UserBalance, error)
		UpsertUserWallet(data model.UserWallet) error
		BatchInsertUserWallet(data []*model.UserWallet) error
	}

	VerifyRepo interface {
		CreateOrder(order *model.HyperLiquidVerifyOrder) error
		UpdateOrder(order *model.HyperLiquidVerifyOrder) error
		GetUnverifiedOrder() ([]*model.HyperLiquidVerifyOrder, error)
		GetOrdersForCSVVerification(csvData string) ([]*model.HyperLiquidVerifyOrder, error)
		ExistsByCloid(cloid string) (bool, error)
		GetOrderByOid(oid string) (*model.HyperLiquidVerifyOrder, error)
		GetOrderByCloid(cloid string) (*model.HyperLiquidVerifyOrder, error)
		UpdateOrderInformationByOid(order *model.HyperLiquidVerifyOrder) error
		UpdateTransactionByOid(order *model.HyperLiquidVerifyOrder) error
		UpdateOrderInformationByCloid(order *model.HyperLiquidVerifyOrder) error
		UpdateTransactionByCloid(order *model.HyperLiquidVerifyOrder) error
		CreateOrders(orders []*model.HyperLiquidVerifyOrder) error
		GetOrderByTokenAndCriteria(req request.ReqTransaction, userID uuid.UUID) (*model.HyperLiquidVerifyOrder, error)
		UpdateTransactionByToken(order *model.HyperLiquidVerifyOrder) error
	}

	AggregateRepo interface {
		SwapMetaData() (*config.MetaData, error)
	}
)
