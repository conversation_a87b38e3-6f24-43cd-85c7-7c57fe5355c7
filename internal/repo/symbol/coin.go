package symbol

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/redis/go-redis/v9"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/cache"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/common/constant"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/model"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/hyperliquid"
	symbolModel "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/symbol"
	"golang.org/x/net/context"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

type CoinRepository struct {
}

func (r *CoinRepository) GetSymbolOneDayRange(symbol string) (float64, float64, error) {
	type oneDayResult struct {
		MaxHigh float64
		MinLow  float64
	}

	var (
		oneDayRes oneDayResult
		endTime   = time.Now().UTC().Format(time.DateTime)
		startTime = time.Now().Add(-24 * time.Hour).UTC().Format(time.DateTime)
		tableName = model.OHLC{}.TableName() + "_1m"
	)
	err := global.GVA_CLICKHOUSE.Table(tableName).
		Select("max(high) as max_high, min(low) as min_low").
		Where("symbol = ? AND time_open >= ? AND time_close <= ?", symbol, startTime, endTime).
		Scan(&oneDayRes).Error

	return oneDayRes.MaxHigh, oneDayRes.MinLow, err
}

func (r *CoinRepository) GetSymbolsData(whereCondition, orderClause string) ([]*model.CoinData, error) {
	var data []*model.CoinData
	err := global.GVA_DB.Model(&model.Coin{}).
		Select("coin.*", "coin_statistic.*").
		Joins("JOIN coin_statistic ON coin.symbol = coin_statistic.symbol").
		Where("coins.is_delisted = ?", false).
		Where(whereCondition).
		Order(orderClause).
		Scan(&data).Error
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (r *CoinRepository) GetSymbolOverallData(symbol string) (*model.Coin, error) {
	var data model.Coin
	result := global.GVA_DB.Model(&model.Coin{}).
		Where("symbol = ?", symbol).
		First(&data)
	if result.Error != nil {
		return nil, result.Error
	}

	return &data, nil
}

func (r *CoinRepository) GetSymbolStatistic(symbol string) (*model.CoinStatistic, error) {
	var data model.CoinStatistic
	result := global.GVA_DB.Model(&model.CoinStatistic{}).
		Where("symbol = ?", symbol).
		First(&data)
	if result.Error != nil {
		return nil, result.Error
	}

	return &data, nil
}

func (r *CoinRepository) GetCategory() ([]string, error) {
	var categories []string
	result := global.GVA_DB.Model(&model.CategoryCoin{}).
		Distinct("category_name").
		Find(&categories)
	if result.Error != nil {
		return nil, result.Error
	}

	return categories, nil
}

func (r *CoinRepository) GetSymbolDataByCategory(categoryName string) ([]*model.CoinData, error) {
	var results []*model.CoinData
	err := global.GVA_DB.Model(&model.Coin{}).
		Select("coin.*, coin_statistic.*").
		Joins("JOIN coin_statistic ON coin.symbol = coin_statistic.symbol").
		Joins("JOIN category_coin ON coin.symbol = category_coin.coin_symbol").
		Where("coins.is_delisted = ?", false).
		Where("category_coin.category_name = ?", categoryName).
		Order("coin_statistic.day_base_vlm DESC").
		Scan(&results).Error

	if err != nil {
		return nil, fmt.Errorf("error fetching coin data by category: %w", err)
	}

	return results, nil
}

func (r *CoinRepository) UpdateSymbolData(symbolList []model.Coin, symbolStatistic []model.CoinStatistic) error {
	tx := global.GVA_DB.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	for i := range symbolList {
		// Check if symbol overall data is changed
		if overallData, ok := symbolModel.OverallDataMap[symbolList[i].Symbol]; !ok || overallData.String() != symbolList[i].String() {
			resultInfo := tx.Clauses(clause.OnConflict{
				Columns: []clause.Column{{Name: "symbol"}},
				DoUpdates: clause.AssignmentColumns([]string{
					"sz_decimals",
					"max_leverage",
					"margin_table_id",
					"is_delisted",
					"only_isolated",
					"last_updated"}),
			}).Create(&symbolList[i])

			if resultInfo.Error != nil {
				tx.Rollback()
				return resultInfo.Error
			}

			symbolModel.OverallDataMap[symbolList[i].Symbol] = symbolList[i]
		}

		var currentStatistic model.CoinStatistic
		result := tx.Model(&model.CoinStatistic{}).
			Where("symbol = ?", symbolList[i].Symbol).
			First(&currentStatistic)
		if result.Error != nil && !errors.Is(result.Error, gorm.ErrRecordNotFound) {
			tx.Rollback()
			return fmt.Errorf("failed to fetch total_supply for %s: %w", symbolStatistic[i].Symbol, result.Error)
		}

		symbolStatistic[i].MarketCap = currentStatistic.CirculatingSupply * symbolStatistic[i].MarkPx
		symbolStatistic[i].CirculatingSupply = currentStatistic.CirculatingSupply
		symbolStatistic[i].TotalSupply = currentStatistic.TotalSupply
		if _, ok := hyperliquid.CurrentSymbolMap[symbolList[i].Symbol]; ok {
			symbolStatistic[i].MarketCap /= 1000
		}
		resultStatistic := tx.Clauses(clause.OnConflict{
			Columns: []clause.Column{{Name: "symbol"}},
			DoUpdates: clause.AssignmentColumns([]string{
				"funding",
				"market_cap",
				"open_interest",
				"prev_day_px",
				"day_ntl_vlm",
				"premium",
				"oracle_px",
				"mark_px",
				"mid_px",
				"impact_px_bid",
				"impact_px_ask",
				"day_base_vlm",
				"change_px",
				"change_px_percent",
				"last_updated"}),
		}).Create(&symbolStatistic[i])

		if resultStatistic.Error != nil {
			tx.Rollback()
			return resultStatistic.Error
		}
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

func (r *CoinRepository) CacheSymbolData(data any, cacheKey string, cacheTime time.Duration) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to marshal symbol data %s: %w", cacheKey, err)
	}
	err = global.GVA_REDIS.Set(context.Background(), cacheKey, jsonData,
		cacheTime).Err()
	if err != nil {
		return fmt.Errorf("failed to cache to Redis %s: %w", cacheKey, err)
	}
	return nil
}

func (r *CoinRepository) GetSymbolList() ([]string, error) {
	list := []string{}
	err := global.GVA_DB.Model(&model.Coin{}).
		Select("symbol").
		Where("is_delisted = false or (is_delisted = true and  only_isolated = true)").
		Find(&list).Error
	if err != nil {
		return list, err
	}

	return list, nil
}

func (r *CoinRepository) UpdateTokenAddressData(symbolOverallData []model.Coin) error {
	for _, data := range symbolOverallData {
		err := global.GVA_DB.Model(&model.Coin{}).
			Where("symbol = ?", data.Symbol).
			Updates(map[string]interface{}{
				"address": data.Address,
			}).Error
		if err != nil {
			return err
		}
	}

	return nil
}

func (r *CoinRepository) UpdateTotalSymbolData(symbolStatistics []model.CoinStatistic) error {
	for _, symbolStatistic := range symbolStatistics {
		err := global.GVA_DB.Model(&model.CoinStatistic{}).
			Where("symbol = ?", symbolStatistic.Symbol).
			Updates(map[string]interface{}{
				"total_supply":       symbolStatistic.TotalSupply,
				"circulating_supply": symbolStatistic.CirculatingSupply,
			}).Error
		if err != nil {
			return err
		}
	}

	return nil
}

func (r *CoinRepository) GetNewSymbolFromCache() ([]string, error) {
	var newSymbolList []string
	key := cache.KeyNewSymbol()
	ctx := context.Background()
	str, err := global.GVA_REDIS.Get(ctx, key).Result()
	if err == nil && str != "" {
		if err = json.Unmarshal([]byte(str), &newSymbolList); err == nil {
			return newSymbolList, nil
		}
	}

	return newSymbolList, nil
}

func (r *CoinRepository) CacheNewSymbol(symbolList []string) error {
	key := cache.KeyNewSymbol()
	ctx := context.Background()
	data, err := json.Marshal(symbolList)
	if err == nil {
		_ = global.GVA_REDIS.Set(ctx, key, data, constant.TenMinuteCacheTime).Err()
		return nil
	}

	return err
}

func (r *CoinRepository) GetNewSymbolFromDB() ([]string, error) {
	var newSymbolList []string
	err := global.GVA_DB.Model(&model.Coin{}).
		Select("symbol").
		Order("created_at desc").
		Limit(20).
		Find(&newSymbolList).Error

	return newSymbolList, err
}

func (r *CoinRepository) UpdatePopularSymbol(symbol string) error {
	_, err := global.GVA_REDIS.ZIncrBy(context.Background(), cache.KeyPopularSymbol(), 1, symbol).Result()
	return err
}

func (r *CoinRepository) GetPopularSymbol(number int64) ([]redis.Z, error) {
	return global.GVA_REDIS.ZRevRangeWithScores(context.Background(), cache.KeyPopularSymbol(), 0, number-1).Result()
}
