package utils

const (
	TimeFormatMilliseconds = "2006-01-02 15:04:05.000"
	TimeFormatSeconds      = "2006-01-02 15:04:05"
)

var IntervalMap = map[string]string{
	"ONE_MINUTE":      "1m",
	"THREE_MINUTES":   "3m",
	"FIVE_MINUTES":    "5m",
	"FIFTEEN_MINUTES": "15m",
	"THIRTY_MINUTES":  "30m",
	"ONE_HOUR":        "1h",
	"TWO_HOURS":       "2h",
	"FOUR_HOURS":      "4h",
	"EIGHT_HOURS":     "8h",
	"TWELVE_HOURS":    "12h",
	"ONE_DAY":         "1d",
	"THREE_DAYS":      "3d",
	"ONE_WEEK":        "1w",
	"ONE_MONTH":       "1M",
}

const (
	TaskSyncRangoMetadata = iota
	TaskCrawlSymbolList
	TaskCrawlCoinMarketCap
	TaskCrawlCategoryList
	TaskTrackUserBalance
	TaskProcessHyperliquidOrder
	TaskSyncRelayMetadata
	TaskSyncRelayCapacity
)

const (
	NotificationTypePrice     = "price"
	NotificationTypePercent   = "percent"
	NotificationDirectionUp   = "up"
	NotificationDirectionDown = "down"
)

const (
	WalletBalanceStream  = "dex_wallet_balance_stream"
	WalletBalanceSubject = "dex.indexer.assets"

	UserSyncInfoStream   = "user_sync_info_stream"
	UserSyncInfoSubject  = "dex.user_service.sync_info"
	UserSyncInfoConsumer = "user_sync_info_consumer"

	RawPricesStream         = "raw_prices_stream"
	RawPricesSubject        = "dex.raw.prices"
	RawPricesConsumer       = "raw_prices_consumer"
	ProcessedPricesStream   = "processed_prices_stream"
	ProcessedPricesSubject  = "dex.processed.prices"
	ProcessedPricesConsumer = "processed_prices_consumer"

	NotificationDispatchSubject = "xbit_notification.dex.price_alert"

	HyperliquidTransactionStream = "dex_hyperliquid_transaction"
	HyperliquidTransactionEvent  = "hyperliquid.tx.>"
	HyperliquidVerifySubject     = "hyperliquid.tx.event"
)

const (
	NativeToken        = "ARB"
	WalletType         = "Futures"
	NativeTokenAddress = "******************************************"
)
