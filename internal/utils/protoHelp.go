/*
 * Copyright © 2023 Xbit Protocol
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package utils

import (
	"encoding/hex"
	"encoding/json"
	"errors"
	"strconv"
	"strings"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/crypto"
)

func ForceJsonMarshal(s interface{}) ([]byte, error) {
	if s == nil {
		return nil, errors.New("nil pointer")
	}
	var (
		data []byte
		err  error
	)
	data, err = json.Marshal(s)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func ForceJsonUnmarshal[T any](s []byte, data T) (T, error) {
	if len(s) == 0 {
		return data, nil
	}
	if string(s) == "null" || string(s) == "<null>" {
		return data, nil
	}
	err := json.Unmarshal(s, &data)
	if err != nil {
		return data, err
	}
	return data, nil
}

func PackedBytesToHash(packed []byte) (string, error) {
	// Calculate keccak256
	hash := crypto.Keccak256(packed)
	var result [32]byte
	copy(result[:], hash)
	return hex.EncodeToString(result[:]), nil
}

func MapToArr[T any](data map[string]T) []T {
	var res []T
	for _, v := range data {
		res = append(res, v)
	}
	return res
}

func MapArrToArr[T any](data [][][]map[string]T) []T {
	var resMap = make(map[string]T)
	for _, fMap := range data {
		for _, sMap := range fMap {
			for _, tMap := range sMap {
				for k, v := range tMap {
					resMap[k] = v
				}
			}
		}
	}
	return MapToArr(resMap)
}

func ForceHexToAddress(s string) common.Address {
	if s == "" {
		return common.Address{}
	}
	return common.HexToAddress(s)
}

func BoolToInt64(b bool) int64 {
	if b {
		return 1
	} else {
		return 0
	}
}

func Int64ToBool(b int64) bool {
	if b == 1 {
		return true
	} else {
		return false
	}
}

func StringPointerToString(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

func IntPointerToInt64(p *int) int64 {
	if p == nil {
		return 0
	}
	return int64(*p)
}

func IntPointerToInt(p *int) int {
	if p == nil {
		return 0
	}
	return *p
}

func StringToFloat(s string) float64 {
	f, err := strconv.ParseFloat(s, 64)
	if err != nil {
		return 0
	}
	return f
}
func HexToDecimalString(hexStr string) string {
	if !strings.HasPrefix(hexStr, "0x") {
		return hexStr
	}
	s := strings.TrimPrefix(hexStr, "0x")
	if s == "" {
		return ""
	}
	v, err := strconv.ParseInt(s, 16, 64)
	if err != nil {
		return hexStr
	}
	return strconv.FormatInt(v, 10)
}
func DecimalToHexString(decimalStr string) string {
	if decimalStr == "" {
		return ""
	}
	v, err := strconv.ParseInt(decimalStr, 10, 64)
	if err != nil {
		return decimalStr
	}
	return "0x" + strconv.FormatInt(v, 16)
}
