package utils

import (
	"bytes"
	"fmt"
	"io"

	"github.com/pierrec/lz4/v4"
)

var (
	TxStatusVerify = Rules{"RequestId": {NotEmpty()}, "TxHash": {NotEmpty()}, "Step": {Ge("0")}}
	CreateTxVerify = Rules{"RequestId": {NotEmpty()}}
)

func LZ4Compress(data []byte) ([]byte, error) {
	var buf bytes.Buffer
	zw := lz4.NewWriter(&buf)

	zw.Apply(lz4.CompressionLevelOption(lz4.Level4))

	_, err := zw.Write(data)
	if err != nil {
		return nil, fmt.Errorf("failed to write data for compression: %w", err)
	}

	err = zw.Close()
	if err != nil {
		return nil, fmt.Errorf("failed to close lz4 writer: %w", err)
	}

	return buf.Bytes(), nil
}

func LZ4Decompress(compressedData []byte) ([]byte, error) {
	zr := lz4.NewReader(bytes.NewReader(compressedData))

	var buf bytes.Buffer
	_, err := io.Copy(&buf, zr)
	if err != nil {
		return nil, fmt.Errorf("failed to decompress data: %w", err)
	}

	return buf.Bytes(), nil
}
