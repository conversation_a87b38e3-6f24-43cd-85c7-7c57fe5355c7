package initializer

import (
	"bytes"
	"flag"
	"fmt"
	"os"
	"path/filepath"

	"github.com/fsnotify/fsnotify"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"github.com/spf13/viper"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/initializer/internal"

	"strings"
	"text/template"
)

// Viper Priority: Command line > Environment variable > Default value
func Viper(path ...string) *viper.Viper {
	//1. assign to config
	var config string

	if len(path) == 0 {
		flag.StringVar(&config, "c", "", "choose config file.")
		flag.Parse()
		if config == "" {
			if configEnv := os.Getenv(internal.ConfigEnv); configEnv == "" {
				switch gin.Mode() {
				case gin.DebugMode:
					config = internal.ConfigDefaultFile
				case gin.ReleaseMode:
					config = internal.ConfigReleaseFile
				case gin.TestMode:
					config = internal.ConfigTestFile
				}
				fmt.Printf("you are using the %s environment name in gin mode, and the path of the config is %s", gin.Mode(), config)
			} else {
				// If the internal.ConfigEnv is not empty, assign the mode to the config field and
				// read the configuration information from the configuration.
				config = configEnv
				fmt.Printf("you are using the %s environment name in gin mode, and the path of the config is %s\n", internal.ConfigEnv, config)
			}
		} else {
			fmt.Printf("you are using the vlaue passed by the -c model parameter of the command line ,config path is %s\n", config)
		}
	} else {
		config = path[0]
		fmt.Printf("you are using the value passed by  func Viper(),config path is%s\n", config)
	}

	//Get STAGE variable to select the .env file
	var stage string
	if stage = os.Getenv("STAGE"); stage == "" {
		stage = "local" // Default stage if not explicitly set
		fmt.Printf("STAGE environment variable not set, defaulting to '%s'.\n", stage)
		// Optionally, set the STAGE env var here so it's available for godotenv.
		os.Setenv("STAGE", stage)
	} else {
		fmt.Printf("STAGE environment variable set to '%s'.\n", stage)
	}
	envFilePath := filepath.Join("env", fmt.Sprintf("%s.env", stage))
	err := godotenv.Load(envFilePath)
	if err != nil {
		fmt.Printf("Warning: Could not load .env file %s. Error: %v. Proceeding without it.\n", envFilePath, err)
	} else {
		fmt.Printf("Successfully loaded environment variables from: %s\n", envFilePath)
	}

	// re-generate yaml template from env
	envVars := getEnvMap()
	tmpl, err := template.ParseFiles("config.yaml")
	if err != nil {
		panic(fmt.Errorf("Fatal error parse config: %s \n", err))
	}

	var buf bytes.Buffer
	err = tmpl.Execute(&buf, envVars)

	if err != nil {
		panic(fmt.Errorf("Fatal error parse config with env: %s \n", err))
	}

	renderedYaml := buf.String()

	v := viper.New()
	v.SetConfigType("yaml")
	err = v.ReadConfig(strings.NewReader(renderedYaml))

	if err != nil {
		panic(fmt.Errorf("Fatal error config file: %s \n", err))
	}
	v.WatchConfig()

	v.OnConfigChange(func(e fsnotify.Event) {
		fmt.Println("config file changed:", e.Name)
		if err = v.Unmarshal(&global.GVA_CONFIG); err != nil {
			fmt.Println(err)
		}
	})
	if err = v.Unmarshal(&global.GVA_CONFIG); err != nil {
		panic(err)
	}

	// Adaptability of the root:
	// Find the corresponding migration location according to the position of the root to ensure the validity of the root path.
	//global.GVA_CONFIG.AutoCode.Root, _ = filepath.Abs("..")
	return v
}

func getEnvMap() map[string]string {
	envMap := make(map[string]string)
	for _, env := range os.Environ() {
		parts := strings.SplitN(env, "=", 2)
		if len(parts) == 2 {
			envMap[parts[0]] = parts[1]
		}
	}
	return envMap
}
