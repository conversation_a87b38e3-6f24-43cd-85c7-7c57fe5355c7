package initializer

import (
	"context"
	repoAsset "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/asset"
	repoTrade "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/trade"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/task/asset"

	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	repoSwap "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/swap"
	repoSymbol "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/symbol"
	repoOrder "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/repo/verify"
	task2 "gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/task"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/task/swap"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/task/symbol"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/service/task/verify"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/utils"
	"go.uber.org/zap"
)

func InitTask() {
	scheduler := task2.NewTaskScheduler()
	ctx, cancel := context.WithCancel(context.Background())
	scheduler.SetCancelFunc(cancel)
	go scheduler.RunWithSignal(ctx)
	swapTask := swap.NewSwapTask(repoSwap.Swap)
	err := scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskSyncRangoMetadata].ID, global.GVA_CONFIG.CronTasks[utils.TaskSyncRangoMetadata].Cron, swapTask.SyncRangoMeta())
	if err != nil {
		global.GVA_LOG.Error("task  = ", zap.Any("err", err))
	}

	symbolTask := symbol.NewSymbolTask(repoSymbol.Symbol, global.GVA_NATS_DEX)
	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskCrawlSymbolList].ID, global.GVA_CONFIG.CronTasks[utils.TaskCrawlSymbolList].Cron, symbolTask.CrawSymbolList())
	if err != nil {
		global.GVA_LOG.Error("task  = ", zap.Any("err", err))
	}

	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskCrawlCoinMarketCap].ID, global.GVA_CONFIG.CronTasks[utils.TaskCrawlCoinMarketCap].Cron, symbolTask.CrawlMarketCap())
	if err != nil {
		global.GVA_LOG.Error("task  = ", zap.Any("err", err))
	}

	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskCrawlCategoryList].ID, global.GVA_CONFIG.CronTasks[utils.TaskCrawlCategoryList].Cron, symbolTask.CrawCategoryList())
	if err != nil {
		global.GVA_LOG.Error("task  = ", zap.Any("err", err))
	}

	// todo: check Error for this scheduler
	assetTask := asset.NewAssetTask(repoAsset.Asset, repoTrade.Trade, repoSymbol.Symbol, global.GVA_NATS_MEME)
	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskTrackUserBalance].ID, global.GVA_CONFIG.CronTasks[utils.TaskTrackUserBalance].Cron, assetTask.TrackUserBalances(ctx))
	if err != nil {
		global.GVA_LOG.Error("task  = ", zap.Any("err", err))
	}

	verifyTask := verify.NewVerifyProcessor(repoOrder.Order)
	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskProcessHyperliquidOrder].ID, global.GVA_CONFIG.CronTasks[utils.TaskProcessHyperliquidOrder].Cron, verifyTask.VerifyBuildFee(ctx))
	if err != nil {
		global.GVA_LOG.Error("task  = ", zap.Any("err", err))
	}

	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskSyncRelayMetadata].ID, global.GVA_CONFIG.CronTasks[utils.TaskSyncRelayMetadata].Cron, swapTask.SyncRelayMeta())
	if err != nil {
		global.GVA_LOG.Error("task  = ", zap.Any("err", err))
	}

	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskSyncRelayCapacity].ID, global.GVA_CONFIG.CronTasks[utils.TaskSyncRelayCapacity].Cron, swapTask.SyncRelayCapacity())
	if err != nil {
		global.GVA_LOG.Error("task  = ", zap.Any("err", err))
	}
}
