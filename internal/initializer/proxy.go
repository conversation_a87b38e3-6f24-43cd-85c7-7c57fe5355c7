package initializer

import (
	"fmt"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/internal/global"
	"gitlab.xbit.live/xbit/xbit-dex/xbit-goback/server/pkg/proxy"
	"go.uber.org/zap"
	"time"
)

// InitProxyManager initializes the global proxy manager
func InitProxyManager() {
	if !global.GVA_CONFIG.Proxy.Enabled {
		global.GVA_LOG.Info("Proxy is disabled in configuration")
		global.GVA_PROXY_MANAGER = nil
		return
	}

	proxyList := global.GVA_CONFIG.Proxy.ProxyList
	if proxyList == "" {
		global.GVA_LOG.Warn("Proxy is enabled but no proxy list provided")
		global.GVA_PROXY_MANAGER = nil
		return
	}

	// Create proxy manager
	proxyManager := proxy.NewProxyManager(proxyList)
	global.GVA_PROXY_MANAGER = proxyManager

	global.GVA_LOG.Info("Proxy manager initialized", 
		zap.Int("proxy_count", proxyManager.GetProxyCount()),
		zap.Strings("proxies", proxyManager.GetAllProxies()))

	// Test proxies if enabled
	if global.GVA_CONFIG.Proxy.TestOnInit {
		testURL := global.GVA_CONFIG.Proxy.TestURL
		if testURL == "" {
			testURL = "https://api.hyperliquid.xyz/info"
		}

		global.GVA_LOG.Info("Testing proxies...", zap.String("test_url", testURL))
		
		testResults := proxyManager.TestAllProxies(testURL, 10*time.Second)
		workingCount := 0
		failedCount := 0

		for proxyURL, err := range testResults {
			if err == nil {
				workingCount++
				global.GVA_LOG.Info("Proxy test passed", zap.String("proxy", proxyURL))
			} else {
				failedCount++
				global.GVA_LOG.Warn("Proxy test failed", 
					zap.String("proxy", proxyURL), 
					zap.Error(err))
			}
		}

		global.GVA_LOG.Info("Proxy test completed", 
			zap.Int("working", workingCount),
			zap.Int("failed", failedCount),
			zap.Int("total", len(testResults)))
	}
}

// GetProxyManager returns the global proxy manager
func GetProxyManager() *proxy.ProxyManager {
	if global.GVA_PROXY_MANAGER == nil {
		return nil
	}
	
	if pm, ok := global.GVA_PROXY_MANAGER.(*proxy.ProxyManager); ok {
		return pm
	}
	
	return nil
}

// IsProxyEnabled returns true if proxy is enabled and available
func IsProxyEnabled() bool {
	return global.GVA_CONFIG.Proxy.Enabled && GetProxyManager() != nil
}

// CreateProxyHTTPClient creates a new HTTP client with proxy support
func CreateProxyHTTPClient(timeout time.Duration) *proxy.ProxyHTTPClient {
	pm := GetProxyManager()
	if pm == nil {
		// Return a client that will use no proxy
		pm = proxy.NewProxyManager("")
	}
	
	return proxy.NewProxyHTTPClient(pm, timeout)
}
